# 自动化任务管理工具详细布局设计规范

## 📋 文档概述

### 文档目的
本文档基于需求设计说明书中的界面布局详细设计，提供了自动化任务管理工具各个页面的具体布局规范、尺寸定义、组件排列和交互细节，为前端开发提供精确的实现指导。

### 设计原则
- **网格系统**: 采用12列网格系统，确保布局的一致性
- **响应式设计**: 适配不同屏幕尺寸和分辨率
- **信息层次**: 通过布局体现信息的重要性层次
- **用户体验**: 优化操作流程和视觉引导

### 目标分辨率
- **最小支持**: 1024x768
- **推荐分辨率**: 1920x1080
- **最大支持**: 3840x2160 (4K)

## 📑 目录

1. [整体布局架构](#整体布局架构)
2. [首页布局设计](#首页布局设计)
3. [任务管理页面布局](#任务管理页面布局)
4. [脚本管理页面布局](#脚本管理页面布局)
5. [系统设置页面布局](#系统设置页面布局)
6. [对话框布局设计](#对话框布局设计)
7. [响应式布局适配](#响应式布局适配)

## 🏗️ 整体布局架构

### 1.1 主窗口结构

#### 窗口尺寸规范
```css
.main-window {
    /* 窗口尺寸 */
    min-width: 1024px;              /* 最小宽度 */
    min-height: 768px;              /* 最小高度 */
    width: 1200px;                  /* 默认宽度 */
    height: 800px;                  /* 默认高度 */
    
    /* 窗口位置 */
    position: center;               /* 居中显示 */
    
    /* 窗口属性 */
    resizable: true;                /* 可调整大小 */
    maximizable: true;              /* 可最大化 */
    minimizable: true;              /* 可最小化 */
}
```

#### 布局区域划分
```
┌─────────────────────────────────────────────────────────────┐
│                    菜单栏 (24px)                            │
├─────────────────────────────────────────────────────────────┤
│                    工具栏 (48px)                            │
├─────────────┬───────────────────────────────────────────────┤
│             │                                               │
│  侧边导航   │              主内容区域                       │
│  (240px)    │           (自适应宽度)                        │
│             │                                               │
│             │                                               │
├─────────────┴───────────────────────────────────────────────┤
│                    状态栏 (24px)                            │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 菜单栏设计

#### 菜单栏规范
```css
.menubar {
    height: 24px;
    background: #f0f0f0;
    border-bottom: 1px solid #d0d0d0;
    padding: 0 8px;
    display: flex;
    align-items: center;
}

.menu-item {
    padding: 4px 12px;
    font-size: 14px;
    color: #333;
    cursor: pointer;
    border-radius: 2px;
}

.menu-item:hover {
    background: #e0e0e0;
}
```

#### 菜单结构
- **文件**: 新建、打开、保存、导入、导出、退出
- **编辑**: 撤销、重做、复制、粘贴、删除
- **视图**: 刷新、全屏、缩放、主题切换
- **工具**: 选项、插件、更新检查
- **帮助**: 用户手册、关于

### 1.3 工具栏设计

#### 工具栏规范
```css
.toolbar {
    height: 48px;
    background: #fafafa;
    border-bottom: 1px solid #e0e0e0;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.toolbar-group {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 0 8px;
    border-right: 1px solid #e0e0e0;
}

.toolbar-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.toolbar-btn:hover {
    background: #e0e0e0;
}
```

#### 工具栏按钮组
- **文件操作**: 新建、打开、保存
- **编辑操作**: 撤销、重做、复制、粘贴
- **视图操作**: 刷新、搜索、筛选
- **执行操作**: 运行、停止、调试

### 1.4 侧边导航设计

#### 导航树规范
```css
.sidebar {
    width: 240px;
    min-width: 180px;
    max-width: 300px;
    background: #ffffff;
    border-right: 1px solid #e0e0e0;
    overflow-y: auto;
}

.nav-group {
    margin-bottom: 16px;
}

.nav-group-title {
    padding: 8px 16px;
    font-size: 12px;
    font-weight: 600;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    margin: 2px 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.nav-item:hover {
    background: #f0f0f0;
}

.nav-item.active {
    background: #1976d2;
    color: white;
}

.nav-icon {
    width: 20px;
    height: 20px;
    margin-right: 12px;
    color: #666;
}

.nav-text {
    font-size: 14px;
    flex: 1;
}
```

#### 导航结构
```
首页
├─ 概览
├─ 统计
└─ 快速操作

任务管理
├─ 任务列表
├─ 任务监控
├─ 执行历史
└─ 任务编排

脚本管理
├─ 脚本列表
├─ 脚本编辑器
├─ 版本管理
└─ 模板库

系统设置
├─ 基本设置
├─ 日志设置
├─ 安全设置
└─ 关于
```

### 1.5 主内容区设计

#### 标签页容器
```css
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #ffffff;
    overflow: hidden;
}

.tab-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.tab-header {
    height: 40px;
    background: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    overflow-x: auto;
}

.tab-item {
    height: 40px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    background: transparent;
    border: none;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    white-space: nowrap;
    transition: all 0.2s ease;
}

.tab-item:hover {
    background: #e0e0e0;
}

.tab-item.active {
    background: #ffffff;
    border-bottom-color: #1976d2;
    color: #1976d2;
}

.tab-content {
    flex: 1;
    overflow: hidden;
    background: #ffffff;
}
```

### 1.6 状态栏设计

#### 状态栏规范
```css
.statusbar {
    height: 24px;
    background: #f0f0f0;
    border-top: 1px solid #d0d0d0;
    display: flex;
    align-items: center;
    padding: 0 16px;
    font-size: 12px;
    color: #666;
}

.status-item {
    display: flex;
    align-items: center;
    margin-right: 16px;
}

.status-icon {
    width: 12px;
    height: 12px;
    margin-right: 4px;
}

.status-separator {
    width: 1px;
    height: 12px;
    background: #d0d0d0;
    margin: 0 8px;
}
```

#### 状态信息内容
- **系统状态**: 运行状态指示器
- **任务统计**: 活跃任务数量
- **资源使用**: CPU和内存使用率
- **时间信息**: 当前时间
- **版本信息**: 应用版本号

## 🏠 首页布局设计

### 2.1 首页整体布局

#### 网格布局结构
```css
.dashboard-container {
    display: grid;
    grid-template-rows: auto auto 1fr auto;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    padding: 24px;
    height: 100%;
    overflow-y: auto;
}

.stats-panel {
    grid-column: 1 / -1;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    margin-bottom: 8px;
}

.chart-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
}

.recent-tasks-section {
    grid-column: 1 / -1;
    min-height: 300px;
}

.quick-actions-section {
    grid-column: 1 / -1;
    display: flex;
    justify-content: center;
    gap: 16px;
    padding: 16px 0;
}
```

### 2.2 统计卡片设计

#### 统计卡片规范
```css
.stat-card {
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 24px 20px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.stat-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.stat-number {
    font-size: 36px;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 8px;
}

.stat-number.total { color: #1976d2; }
.stat-number.active { color: #2196f3; }
.stat-number.completed { color: #4caf50; }
.stat-number.failed { color: #f44336; }

.stat-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.stat-trend {
    font-size: 12px;
    margin-top: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.stat-trend.up { color: #4caf50; }
.stat-trend.down { color: #f44336; }
.stat-trend.stable { color: #999; }
```

#### 统计卡片内容
1. **总任务数**: 显示系统中所有任务的总数
2. **活跃任务**: 显示当前启用的任务数量
3. **已完成**: 显示最近24小时完成的任务数
4. **失败任务**: 显示最近24小时失败的任务数

### 2.3 图表区域设计

#### 图表面板规范
```css
.chart-panel {
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    min-height: 280px;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;
}

.chart-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.chart-controls {
    display: flex;
    gap: 8px;
}

.chart-content {
    height: 220px;
    position: relative;
}

.chart-legend {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 12px;
    font-size: 12px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
}
```

#### 图表类型
1. **执行成功率饼图**: 显示任务成功/失败比例
2. **执行时长分布柱状图**: 显示任务执行时长分布

### 2.4 最近任务列表

#### 任务列表规范
```css
.recent-tasks-panel {
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    overflow: hidden;
}

.recent-tasks-header {
    padding: 16px 20px;
    background: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.recent-tasks-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.view-all-link {
    font-size: 14px;
    color: #1976d2;
    text-decoration: none;
    font-weight: 500;
}

.recent-tasks-list {
    max-height: 240px;
    overflow-y: auto;
}

.task-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    border-bottom: 1px solid #f0f0f0;
    transition: background 0.2s ease;
}

.task-item:hover {
    background: #f8f8f8;
}

.task-item:last-child {
    border-bottom: none;
}

.task-icon {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 16px;
}

.task-info {
    flex: 1;
    min-width: 0;
}

.task-name {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.task-time {
    font-size: 12px;
    color: #999;
}

.task-status {
    margin-left: 12px;
}
```

### 2.5 快速操作区

#### 快速操作按钮
```css
.quick-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
    align-items: center;
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 16px 20px;
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    text-decoration: none;
    color: #333;
    transition: all 0.2s ease;
    min-width: 120px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.quick-action-btn:hover {
    background: #f8f8f8;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.quick-action-icon {
    width: 32px;
    height: 32px;
    background: rgba(25, 118, 210, 0.1);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #1976d2;
    font-size: 18px;
}

.quick-action-text {
    font-size: 14px;
    font-weight: 500;
    text-align: center;
}
```

#### 快速操作项目
1. **新建任务**: 跳转到任务创建页面
2. **新建脚本**: 跳转到脚本编辑器
3. **查看日志**: 跳转到系统日志页面
4. **系统设置**: 打开设置对话框

## 📋 任务管理页面布局

### 3.1 任务列表页面

#### 页面整体结构
```css
.task-management-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 24px;
    gap: 16px;
}

.task-toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background: #f5f5f5;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    flex-wrap: wrap;
    gap: 12px;
}

.task-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

.task-filters {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.task-table-container {
    flex: 1;
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.task-table-wrapper {
    flex: 1;
    overflow: auto;
}

.task-pagination {
    padding: 16px 20px;
    background: #f5f5f5;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
```

### 3.2 工具栏设计

#### 工具栏按钮组
```css
.toolbar-btn-group {
    display: flex;
    gap: 8px;
}

.toolbar-btn {
    height: 36px;
    padding: 0 16px;
    border: 1px solid #e0e0e0;
    background: #ffffff;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.toolbar-btn:hover {
    background: #f8f8f8;
    border-color: #1976d2;
}

.toolbar-btn.primary {
    background: #1976d2;
    color: white;
    border-color: #1976d2;
}

.toolbar-btn.primary:hover {
    background: #1565c0;
}

.toolbar-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}
```

### 3.3 筛选器设计

#### 筛选器组件
```css
.filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-label {
    font-size: 14px;
    color: #666;
    white-space: nowrap;
}

.filter-select {
    min-width: 120px;
    height: 36px;
    padding: 0 12px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background: #ffffff;
    font-size: 14px;
    color: #333;
}

.filter-select:focus {
    outline: none;
    border-color: #1976d2;
    box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.1);
}

.search-input {
    width: 240px;
    height: 36px;
    padding: 0 12px 0 36px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    background: #ffffff;
    font-size: 14px;
    color: #333;
    position: relative;
}

.search-input:focus {
    outline: none;
    border-color: #1976d2;
    box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.1);
}

.search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    font-size: 16px;
    pointer-events: none;
}
```

### 3.4 任务表格设计

#### 表格基础规范
```css
.task-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.task-table th {
    height: 48px;
    padding: 12px 16px;
    background: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
    font-weight: 600;
    color: #333;
    text-align: center;
    position: sticky;
    top: 0;
    z-index: 10;
}

.task-table th.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
}

.task-table th.sortable:hover {
    background: #e8e8e8;
}

.sort-icon {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 12px;
    color: #999;
}

.sort-icon.asc,
.sort-icon.desc {
    color: #1976d2;
}

.task-table td {
    height: 48px;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    text-align: center;
    vertical-align: middle;
}

.task-table tr:hover {
    background: #f8f8f8;
}

.task-table tr.selected {
    background: rgba(25, 118, 210, 0.1);
}

/* 列宽定义 */
.col-checkbox { width: 40px; }
.col-index { width: 60px; }
.col-name { width: 200px; min-width: 150px; }
.col-status { width: 100px; }
.col-type { width: 80px; }
.col-schedule { width: 120px; }
.col-last-run { width: 140px; }
.col-next-run { width: 140px; }
.col-actions { width: 120px; }
```

#### 表格内容设计
```css
.task-name-cell {
    text-align: left;
    display: flex;
    align-items: center;
    gap: 8px;
}

.task-icon {
    width: 20px;
    height: 20px;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    flex-shrink: 0;
}

.task-name {
    font-weight: 500;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.task-description {
    font-size: 12px;
    color: #999;
    margin-top: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    min-width: 70px;
    justify-content: center;
}

.status-badge.active {
    background: rgba(76, 175, 80, 0.1);
    color: #4caf50;
}

.status-badge.inactive {
    background: rgba(255, 152, 0, 0.1);
    color: #ff9800;
}

.status-badge.running {
    background: rgba(33, 150, 243, 0.1);
    color: #2196f3;
}

.status-badge.failed {
    background: rgba(244, 67, 54, 0.1);
    color: #f44336;
}

.status-badge.completed {
    background: rgba(76, 175, 80, 0.1);
    color: #4caf50;
}

.status-indicator {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: currentColor;
}
```

#### 操作按钮设计
```css
.action-buttons {
    display: flex;
    gap: 4px;
    justify-content: center;
}

.action-btn {
    width: 28px;
    height: 28px;
    border: none;
    background: transparent;
    border-radius: 2px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #666;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background: #f0f0f0;
    color: #333;
}

.action-btn.edit:hover {
    background: rgba(33, 150, 243, 0.1);
    color: #2196f3;
}

.action-btn.delete:hover {
    background: rgba(244, 67, 54, 0.1);
    color: #f44336;
}

.action-btn.run:hover {
    background: rgba(76, 175, 80, 0.1);
    color: #4caf50;
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}
```

### 3.5 分页组件设计

#### 分页控件规范
```css
.pagination-info {
    font-size: 14px;
    color: #666;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.page-size-selector {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #666;
}

.page-size-select {
    width: 80px;
    height: 32px;
    padding: 0 8px;
    border: 1px solid #e0e0e0;
    border-radius: 2px;
    background: #ffffff;
    font-size: 14px;
}

.pagination-buttons {
    display: flex;
    gap: 4px;
}

.page-btn {
    width: 32px;
    height: 32px;
    border: 1px solid #e0e0e0;
    background: #ffffff;
    border-radius: 2px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: #333;
    transition: all 0.2s ease;
}

.page-btn:hover {
    background: #f8f8f8;
    border-color: #1976d2;
}

.page-btn.active {
    background: #1976d2;
    color: white;
    border-color: #1976d2;
}

.page-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-ellipsis {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 14px;
}
```

## 📝 脚本管理页面布局

### 4.1 脚本列表页面

#### 页面结构设计
```css
.script-management-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 24px;
    gap: 16px;
}

.script-toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    background: #f5f5f5;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    flex-wrap: wrap;
    gap: 12px;
}

.script-grid-container {
    flex: 1;
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.script-grid {
    flex: 1;
    padding: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
    overflow-y: auto;
}
```

#### 脚本卡片设计
```css
.script-card {
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.script-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    transform: translateY(-2px);
    border-color: #1976d2;
}

.script-card.selected {
    border-color: #1976d2;
    background: rgba(25, 118, 210, 0.05);
}

.script-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.script-type-icon {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 16px;
}

.script-type-icon.python {
    background: rgba(55, 118, 171, 0.1);
    color: #3776ab;
}

.script-type-icon.shell {
    background: rgba(76, 175, 80, 0.1);
    color: #4caf50;
}

.script-type-icon.batch {
    background: rgba(255, 152, 0, 0.1);
    color: #ff9800;
}

.script-info {
    flex: 1;
    min-width: 0;
}

.script-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.script-type {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.script-description {
    font-size: 14px;
    color: #666;
    line-height: 1.4;
    margin-bottom: 12px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.script-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #999;
}

.script-size {
    display: flex;
    align-items: center;
    gap: 4px;
}

.script-modified {
    display: flex;
    align-items: center;
    gap: 4px;
}
```

### 4.2 脚本编辑器布局

#### 编辑器整体结构
```css
.script-editor-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.editor-toolbar {
    height: 48px;
    background: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    padding: 0 16px;
    gap: 8px;
}

.editor-main {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.editor-sidebar {
    width: 200px;
    background: #f8f8f8;
    border-right: 1px solid #e0e0e0;
    overflow-y: auto;
}

.editor-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.editor-tabs {
    height: 36px;
    background: #f0f0f0;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    overflow-x: auto;
}

.editor-pane {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.code-editor {
    flex: 1;
    background: #ffffff;
}

.editor-footer {
    height: 200px;
    background: #f8f8f8;
    border-top: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
}
```

#### 代码编辑器配置
```css
.code-editor .monaco-editor {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
}

.code-editor .margin {
    background-color: #f8f8f8;
}

.code-editor .current-line {
    background-color: rgba(25, 118, 210, 0.05);
}

.code-editor .selected-text {
    background-color: rgba(25, 118, 210, 0.2);
}
```

## ⚙️ 系统设置页面布局

### 5.1 设置对话框设计

#### 对话框整体结构
```css
.settings-dialog {
    width: 800px;
    height: 600px;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.settings-header {
    height: 56px;
    background: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    padding: 0 24px;
}

.settings-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    flex: 1;
}

.settings-close {
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
}

.settings-close:hover {
    background: #e0e0e0;
}

.settings-body {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.settings-sidebar {
    width: 200px;
    background: #f8f8f8;
    border-right: 1px solid #e0e0e0;
    overflow-y: auto;
}

.settings-content {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
}

.settings-footer {
    height: 64px;
    background: #f5f5f5;
    border-top: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 24px;
    gap: 12px;
}
```

### 5.2 设置分类导航

#### 侧边导航设计
```css
.settings-nav {
    padding: 16px 0;
}

.settings-nav-group {
    margin-bottom: 24px;
}

.settings-nav-title {
    padding: 8px 16px;
    font-size: 12px;
    font-weight: 600;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.settings-nav-item {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    margin: 2px 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.2s ease;
    font-size: 14px;
    color: #333;
}

.settings-nav-item:hover {
    background: #e8e8e8;
}

.settings-nav-item.active {
    background: #1976d2;
    color: white;
}

.settings-nav-icon {
    width: 20px;
    height: 20px;
    margin-right: 12px;
    color: inherit;
}
```

### 5.3 设置内容区域

#### 设置表单设计
```css
.settings-section {
    margin-bottom: 32px;
}

.settings-section-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e0e0e0;
}

.settings-group {
    margin-bottom: 24px;
}

.settings-group-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 12px;
}

.settings-item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    gap: 16px;
}

.settings-label {
    min-width: 120px;
    font-size: 14px;
    color: #333;
    font-weight: 500;
}

.settings-control {
    flex: 1;
    max-width: 300px;
}

.settings-description {
    font-size: 12px;
    color: #666;
    margin-top: 4px;
    line-height: 1.4;
}
```

## 💬 对话框布局设计

### 6.1 模态对话框规范

#### 对话框基础结构
```css
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-dialog {
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.modal-close {
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
}

.modal-body {
    padding: 24px;
    overflow-y: auto;
    flex: 1;
}

.modal-footer {
    padding: 16px 24px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 12px;
}
```

### 6.2 确认对话框设计

#### 确认对话框规范
```css
.confirm-dialog {
    width: 400px;
}

.confirm-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px;
    font-size: 24px;
}

.confirm-icon.warning {
    background: rgba(255, 152, 0, 0.1);
    color: #ff9800;
}

.confirm-icon.danger {
    background: rgba(244, 67, 54, 0.1);
    color: #f44336;
}

.confirm-icon.info {
    background: rgba(33, 150, 243, 0.1);
    color: #2196f3;
}

.confirm-message {
    text-align: center;
    font-size: 16px;
    color: #333;
    line-height: 1.5;
    margin-bottom: 8px;
}

.confirm-detail {
    text-align: center;
    font-size: 14px;
    color: #666;
    line-height: 1.4;
}
```

## 📱 响应式布局适配

### 7.1 断点定义

#### 响应式断点
```css
/* 断点定义 */
@media (max-width: 1279px) {
    /* 中等屏幕适配 */
    .sidebar {
        width: 200px;
    }

    .dashboard-container {
        padding: 16px;
        gap: 16px;
    }

    .stats-panel {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 1023px) {
    /* 小屏幕适配 */
    .main-window {
        min-width: 768px;
    }

    .sidebar {
        width: 180px;
    }

    .chart-section {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 767px) {
    /* 移动设备适配 */
    .main-window {
        min-width: 480px;
    }

    .sidebar {
        position: absolute;
        left: -240px;
        z-index: 1000;
        transition: left 0.3s ease;
    }

    .sidebar.open {
        left: 0;
    }

    .dashboard-container {
        grid-template-columns: 1fr;
        padding: 12px;
    }

    .stats-panel {
        grid-template-columns: 1fr;
    }

    .quick-actions {
        flex-direction: column;
        gap: 8px;
    }
}
```

### 7.2 组件响应式适配

#### 表格响应式
```css
@media (max-width: 1023px) {
    .task-table {
        font-size: 12px;
    }

    .task-table th,
    .task-table td {
        padding: 8px 12px;
    }

    .col-description {
        display: none;
    }
}

@media (max-width: 767px) {
    .task-table-container {
        overflow-x: auto;
    }

    .task-table {
        min-width: 600px;
    }

    .col-schedule,
    .col-last-run {
        display: none;
    }
}
```

#### 卡片响应式
```css
@media (max-width: 1023px) {
    .script-grid {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: 12px;
        padding: 16px;
    }
}

@media (max-width: 767px) {
    .script-grid {
        grid-template-columns: 1fr;
        gap: 8px;
        padding: 12px;
    }

    .script-card {
        padding: 12px;
    }
}
```

---

**文档版本**: 1.0
**最后更新**: 2024-12-19
**文档状态**: 已完成
