"""
脚本版本数据模型

定义脚本版本管理相关的数据模型类。
"""

from typing import Optional, Dict, Any
from sqlalchemy import String, Text, ForeignKey, Boolean, Index, JSON
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import BaseModel, AuditMixin


class ScriptVersion(BaseModel, AuditMixin):
    """
    脚本版本模型
    
    存储脚本的版本历史和内容。
    """
    __tablename__ = "script_versions"
    
    # 关联脚本
    script_id: Mapped[int] = mapped_column(
        ForeignKey("scripts.id"),
        nullable=False,
        comment="关联脚本ID"
    )
    
    # 版本信息
    version: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        comment="版本号"
    )
    
    version_name: Mapped[Optional[str]] = mapped_column(
        String(200),
        nullable=True,
        comment="版本名称"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="版本描述"
    )
    
    # 版本内容
    content: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        comment="脚本内容"
    )
    
    file_path: Mapped[Optional[str]] = mapped_column(
        String(500),
        nullable=True,
        comment="文件路径"
    )
    
    # 版本属性
    is_current: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        comment="是否为当前版本"
    )
    
    is_stable: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        comment="是否为稳定版本"
    )
    
    # 变更信息
    change_log: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="变更日志"
    )
    
    # 参数定义
    parameters: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        comment="参数定义"
    )
    
    # 校验信息
    checksum: Mapped[Optional[str]] = mapped_column(
        String(64),
        nullable=True,
        comment="内容校验和"
    )
    
    # 关系定义
    script: Mapped["Script"] = relationship(
        "Script",
        back_populates="versions"
    )
    
    # 索引定义
    __table_args__ = (
        Index('idx_script_version_script_id', 'script_id'),
        Index('idx_script_version_version', 'version'),
        Index('idx_script_version_is_current', 'is_current'),
        Index('idx_script_version_created_at', 'created_at'),
    )
    
    def __init__(self, **kwargs):
        """初始化脚本版本实例"""
        super().__init__(**kwargs)
        if not self.parameters:
            self.parameters = {}
    
    def set_as_current(self) -> None:
        """设置为当前版本"""
        self.is_current = True
    
    def calculate_checksum(self) -> str:
        """计算内容校验和"""
        import hashlib
        return hashlib.sha256(self.content.encode('utf-8')).hexdigest()
    
    def __repr__(self) -> str:
        """字符串表示"""
        return f"<ScriptVersion(id={self.id}, script_id={self.script_id}, version='{self.version}')>"
    
    def __str__(self) -> str:
        """用户友好的字符串表示"""
        return f"ScriptVersion: {self.version} ({'Current' if self.is_current else 'Historical'})"
