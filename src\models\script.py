"""
脚本数据模型

定义脚本相关的数据模型类。
"""

from typing import Optional, List, Dict, Any
from sqlalchemy import String, Text, Enum, Integer, JSON, Index
from sqlalchemy.orm import Mapped, mapped_column, relationship
import enum

from .base import BaseModel, AuditMixin


class ScriptType(enum.Enum):
    """脚本类型枚举"""
    PYTHON = "PYTHON"
    SHELL = "SHELL"
    BATCH = "BATCH"
    POWERSHELL = "POWERSHELL"
    JAVASCRIPT = "JAVASCRIPT"


class ScriptStatus(enum.Enum):
    """脚本状态枚举"""
    DRAFT = "DRAFT"          # 草稿
    ACTIVE = "ACTIVE"        # 活跃
    INACTIVE = "INACTIVE"    # 非活跃
    DEPRECATED = "DEPRECATED" # 已弃用


class Script(BaseModel, AuditMixin):
    """
    脚本模型
    
    存储脚本的基本信息和内容。
    """
    __tablename__ = "scripts"
    
    # 基本信息
    name: Mapped[str] = mapped_column(
        String(200),
        nullable=False,
        comment="脚本名称"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="脚本描述"
    )
    
    script_type: Mapped[ScriptType] = mapped_column(
        Enum(ScriptType),
        nullable=False,
        comment="脚本类型"
    )
    
    status: Mapped[ScriptStatus] = mapped_column(
        Enum(ScriptStatus),
        default=ScriptStatus.DRAFT,
        nullable=False,
        comment="脚本状态"
    )
    
    # 脚本内容
    content: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="脚本内容"
    )
    
    file_path: Mapped[Optional[str]] = mapped_column(
        String(500),
        nullable=True,
        comment="脚本文件路径"
    )
    
    # 版本信息
    version: Mapped[str] = mapped_column(
        String(50),
        default="1.0.0",
        nullable=False,
        comment="脚本版本"
    )
    
    # 参数定义
    parameters: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        comment="脚本参数定义"
    )
    
    # 执行配置
    timeout: Mapped[int] = mapped_column(
        Integer,
        default=3600,
        nullable=False,
        comment="超时时间（秒）"
    )
    
    max_memory: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        comment="最大内存使用（MB）"
    )
    
    # 统计信息
    execution_count: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        comment="执行次数"
    )
    
    success_count: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        comment="成功次数"
    )
    
    # 标签和分类
    tags: Mapped[Optional[List[str]]] = mapped_column(
        JSON,
        nullable=True,
        comment="脚本标签"
    )
    
    category: Mapped[Optional[str]] = mapped_column(
        String(100),
        nullable=True,
        comment="脚本分类"
    )
    
    # 关系定义
    tasks: Mapped[List["Task"]] = relationship(
        "Task",
        back_populates="script",
        cascade="all, delete-orphan"
    )
    
    executions: Mapped[List["Execution"]] = relationship(
        "Execution",
        back_populates="script",
        cascade="all, delete-orphan"
    )
    
    versions: Mapped[List["ScriptVersion"]] = relationship(
        "ScriptVersion",
        back_populates="script",
        cascade="all, delete-orphan"
    )
    
    # 索引定义
    __table_args__ = (
        Index('idx_script_name', 'name'),
        Index('idx_script_type', 'script_type'),
        Index('idx_script_status', 'status'),
        Index('idx_script_category', 'category'),
        Index('idx_script_created_at', 'created_at'),
    )
    
    def __init__(self, **kwargs):
        """初始化脚本实例"""
        super().__init__(**kwargs)
        if not self.parameters:
            self.parameters = {}
        if not self.tags:
            self.tags = []
    
    def add_tag(self, tag: str) -> None:
        """添加标签"""
        if not self.tags:
            self.tags = []
        if tag not in self.tags:
            self.tags.append(tag)
    
    def remove_tag(self, tag: str) -> None:
        """移除标签"""
        if self.tags and tag in self.tags:
            self.tags.remove(tag)
    
    def increment_execution_count(self, success: bool = True) -> None:
        """增加执行计数"""
        self.execution_count += 1
        if success:
            self.success_count += 1
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.execution_count == 0:
            return 0.0
        return (self.success_count / self.execution_count) * 100
    
    @property
    def is_executable(self) -> bool:
        """是否可执行"""
        return (self.status == ScriptStatus.ACTIVE and 
                not self.is_deleted and 
                (self.content or self.file_path))
    
    def validate_parameters(self, params: Dict[str, Any]) -> List[str]:
        """
        验证参数
        
        Args:
            params: 要验证的参数
            
        Returns:
            验证错误列表
        """
        errors = []
        
        if not self.parameters:
            return errors
        
        for param_name, param_def in self.parameters.items():
            if param_def.get('required', False) and param_name not in params:
                errors.append(f"缺少必需参数: {param_name}")
            
            if param_name in params:
                param_value = params[param_name]
                param_type = param_def.get('type', 'string')
                
                # 简单的类型验证
                if param_type == 'integer' and not isinstance(param_value, int):
                    errors.append(f"参数 {param_name} 应为整数类型")
                elif param_type == 'boolean' and not isinstance(param_value, bool):
                    errors.append(f"参数 {param_name} 应为布尔类型")
                elif param_type == 'string' and not isinstance(param_value, str):
                    errors.append(f"参数 {param_name} 应为字符串类型")
        
        return errors
    
    def get_parameter_defaults(self) -> Dict[str, Any]:
        """获取参数默认值"""
        defaults = {}
        if self.parameters:
            for param_name, param_def in self.parameters.items():
                if 'default' in param_def:
                    defaults[param_name] = param_def['default']
        return defaults
    
    def __repr__(self) -> str:
        """字符串表示"""
        return f"<Script(id={self.id}, name='{self.name}', type={self.script_type.value})>"
    
    def __str__(self) -> str:
        """用户友好的字符串表示"""
        return f"Script: {self.name} ({self.script_type.value})"
