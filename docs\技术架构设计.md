# 自动化任务管理工具技术架构设计

## 📋 概述

### 文档定位与适用场景
- **文档类型**: 技术架构实现文档
- **侧重点**: 具体技术实现、代码示例、配置细节、部署方案
- **目标读者**: 开发工程师、运维工程师、技术实施人员
- **使用场景**:
  - 具体功能模块的技术实现
  - 代码开发和调试参考
  - 系统部署和配置指导
  - 技术问题排查和优化
- **配套文档**: 与《系统架构设计规范.md》互补，后者侧重架构设计和规范制定

### 项目背景
本文档详细描述了自动化任务管理工具的技术架构设计，基于需求设计说明书的技术栈选型和已完善的数据模型设计，采用分层架构模式，确保系统的可扩展性、可维护性和高性能。

## 🏗️ 整体架构

### 架构模式
采用**分层架构 + 事件驱动**的混合架构模式：
- **分层架构**：清晰的职责分离，便于维护和测试
- **事件驱动**：异步处理，提高系统响应性和并发能力

### 架构层次

```
┌─────────────────────────────────────────────────────────────┐
│                    表示层 (Presentation Layer)              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  │
│  │   主界面    │  │  任务管理   │  │  脚本管理   │  │  系统设置   │  │
│  │ MainWindow  │  │ TaskWidget  │  │ScriptWidget │  │SettingsWidget│ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                    业务逻辑层 (Business Layer)              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  │
│  │  任务服务   │  │  脚本服务   │  │  执行服务   │  │  监控服务   │  │
│  │TaskService  │  │ScriptService│  │ExecutionSvc │  │MonitorSvc   │  │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘  │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  │
│  │  调度服务   │  │  通知服务   │  │  配置服务   │  │  安全服务   │  │
│  │SchedulerSvc │  │NotificationSvc│ │ ConfigSvc   │  │SecuritySvc  │  │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                   数据访问层 (Data Access Layer)            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  │
│  │   任务DAO   │  │  脚本DAO    │  │  执行DAO    │  │  日志DAO    │  │
│  │  TaskDAO    │  │ ScriptDAO   │  │ExecutionDAO │  │ LogDAO      │  │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘  │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  │
│  │  队列DAO    │  │  通知DAO    │  │  监控DAO    │  │  配置DAO    │  │
│  │ QueueDAO    │  │NotifyDAO    │  │MonitorDAO   │  │ ConfigDAO   │  │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                     数据层 (Data Layer)                     │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │                   SQLite 数据库                         │  │
│  │  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐       │  │
│  │  │  核心表  │ │ 优化表  │ │ 配置表  │ │ 日志表  │       │  │
│  │  │ 9张表   │ │ 3张表   │ │ 2张表   │ │ 2张表   │       │  │
│  │  └─────────┘ └─────────┘ └─────────┘ └─────────┘       │  │
│  └─────────────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 技术栈架构

### 前端技术架构

#### UI框架层
```python
# PyQt6 核心组件
- QApplication: 应用程序主体
- QMainWindow: 主窗口框架
- QWidget: 自定义组件基类
- QThread: UI线程管理
```

#### 界面组件层
```python
# 主要界面组件
- MainWindow: 主窗口和导航管理
- TaskWidget: 任务管理界面
- ScriptWidget: 脚本管理界面
- MonitorWidget: 监控界面
- SettingsWidget: 设置界面
```

#### 图表和可视化
```python
# PyQtChart 图表组件
- QChart: 基础图表类
- QLineSeries: 线性图表（性能趋势）
- QPieSeries: 饼图（任务状态分布）
- QBarSeries: 柱状图（执行统计）
```

#### 代码编辑器
```python
# QScintilla 编辑器组件
- QsciScintilla: 主编辑器类
- QsciLexerPython: Python语法高亮
- QsciLexerBatch: Batch语法高亮
- QsciAPIs: 自动完成API
```

### 后端技术架构

#### 核心服务层
```python
# 业务服务组件
class TaskService:
    """任务管理服务"""
    - create_task()
    - update_task()
    - delete_task()
    - schedule_task()

class ExecutionService:
    """执行引擎服务"""
    - execute_script()
    - monitor_execution()
    - handle_retry()
    - manage_resources()

class SchedulerService:
    """调度服务 - 基于APScheduler"""
    - add_job()
    - remove_job()
    - pause_job()
    - resume_job()
```

#### 多线程架构
```python
# 线程管理策略
- UI线程: PyQt6 主线程，处理界面交互
- 执行线程: QThread，处理脚本执行
- 调度线程: APScheduler，处理任务调度
- 监控线程: 系统资源监控
- 通知线程: 异步通知发送
```

#### 进程管理
```python
# 脚本执行隔离
- subprocess: 脚本进程执行
- multiprocessing: 进程池管理
- RestrictedPython: 安全沙箱
- 资源限制: CPU、内存、时间限制
```

### 数据存储架构

#### ORM模型层
```python
# SQLAlchemy 模型类
- Task: 任务模型
- Script: 脚本模型
- Execution: 执行记录模型
- TaskQueue: 队列模型
- NotificationHistory: 通知历史模型
- SystemMonitor: 监控数据模型
```

#### 数据访问层
```python
# DAO 设计模式
class BaseDAO:
    """基础DAO类"""
    - create()
    - read()
    - update()
    - delete()
    - batch_operation()

class TaskDAO(BaseDAO):
    """任务数据访问"""
    - find_by_status()
    - find_by_schedule()
    - update_execution_status()
```

#### 缓存策略
```python
# 多层缓存架构
- L1缓存: functools.lru_cache (内存)
- L2缓存: diskcache (磁盘)
- 配置缓存: 系统配置热加载
- 查询缓存: 频繁查询结果缓存
```

## 🔧 核心组件设计

### 1. 任务调度引擎

#### 调度器架构
```python
class TaskScheduler:
    """基于APScheduler的任务调度器"""
    
    def __init__(self):
        self.scheduler = AsyncIOScheduler()
        self.job_store = SQLAlchemyJobStore()
        self.executor = ThreadPoolExecutor()
        
    def add_task(self, task: Task):
        """添加任务到调度器"""
        job = self.scheduler.add_job(
            func=self.execute_task,
            trigger=self.create_trigger(task),
            args=[task.id],
            id=f"task_{task.id}",
            max_instances=task.max_concurrent
        )
        
    def create_trigger(self, task: Task):
        """根据任务配置创建触发器"""
        if task.schedule_type == 'cron':
            return CronTrigger.from_crontab(task.schedule_config['cron'])
        elif task.schedule_type == 'interval':
            return IntervalTrigger(seconds=task.schedule_config['interval'])
        elif task.schedule_type == 'once':
            return DateTrigger(run_date=task.schedule_config['execute_time'])
```

#### 队列管理
```python
class TaskQueueManager:
    """任务队列管理器"""
    
    def __init__(self):
        self.priority_queue = PriorityQueue()
        self.worker_pool = ThreadPoolExecutor(max_workers=3)
        
    def enqueue_task(self, task_id: int, priority: int = 5):
        """任务入队"""
        queue_item = TaskQueue(
            task_id=task_id,
            priority=priority,
            status='pending',
            scheduled_time=datetime.now()
        )
        self.queue_dao.create(queue_item)
        
    def dequeue_task(self) -> Optional[TaskQueue]:
        """任务出队"""
        return self.queue_dao.get_next_pending_task()
```

### 2. 脚本执行引擎

#### 执行器设计
```python
class ScriptExecutor:
    """脚本执行引擎"""
    
    def __init__(self):
        self.sandbox = RestrictedPython()
        self.resource_monitor = ResourceMonitor()
        
    async def execute_script(self, script: Script, parameters: dict):
        """异步执行脚本"""
        execution = Execution(
            script_id=script.id,
            status='running',
            start_time=datetime.now(),
            parameters=parameters
        )
        
        try:
            # 创建安全执行环境
            env = self.create_sandbox_environment(script)
            
            # 启动资源监控
            monitor_task = asyncio.create_task(
                self.resource_monitor.monitor_execution(execution.id)
            )
            
            # 执行脚本
            if script.type == 'python':
                result = await self.execute_python_script(script, env)
            elif script.type == 'batch':
                result = await self.execute_batch_script(script, env)
            
            execution.status = 'success'
            execution.result = result
            
        except Exception as e:
            execution.status = 'failed'
            execution.error_message = str(e)
            
        finally:
            execution.end_time = datetime.now()
            monitor_task.cancel()
            self.execution_dao.update(execution)
```

#### 安全沙箱
```python
class SecuritySandbox:
    """安全沙箱环境"""
    
    def __init__(self):
        self.restricted_python = RestrictedPython()
        self.allowed_modules = ['os', 'sys', 'json', 'datetime']
        
    def create_safe_environment(self, script: Script):
        """创建安全执行环境"""
        safe_globals = {
            '__builtins__': self.get_safe_builtins(),
            'print': self.safe_print,
            'open': self.safe_open,
        }
        
        # 根据脚本权限配置允许的模块
        for module in script.required_permissions.get('modules', []):
            if module in self.allowed_modules:
                safe_globals[module] = __import__(module)
                
        return safe_globals
```

### 3. 监控系统

#### 系统监控器
```python
class SystemMonitor:
    """系统性能监控器"""
    
    def __init__(self):
        self.psutil = psutil
        self.collection_interval = 60  # 秒
        
    async def collect_metrics(self):
        """收集系统指标"""
        while True:
            try:
                metrics = SystemMonitorModel(
                    timestamp=datetime.now(),
                    cpu_usage_percent=psutil.cpu_percent(),
                    memory_usage_percent=psutil.virtual_memory().percent,
                    disk_usage_percent=psutil.disk_usage('/').percent,
                    active_tasks_count=self.get_active_tasks_count(),
                    queue_length=self.get_queue_length()
                )
                
                self.monitor_dao.create(metrics)
                
                # 检查告警条件
                await self.check_alerts(metrics)
                
            except Exception as e:
                logger.error(f"监控数据收集失败: {e}")
                
            await asyncio.sleep(self.collection_interval)
```

#### 告警系统
```python
class AlertManager:
    """告警管理器"""
    
    def __init__(self):
        self.alert_rules = self.load_alert_rules()
        self.notification_service = NotificationService()
        
    async def check_alerts(self, metrics: SystemMonitorModel):
        """检查告警条件"""
        for rule in self.alert_rules:
            if self.evaluate_rule(rule, metrics):
                await self.trigger_alert(rule, metrics)
                
    def evaluate_rule(self, rule: dict, metrics: SystemMonitorModel) -> bool:
        """评估告警规则"""
        if rule['metric'] == 'cpu_usage':
            return metrics.cpu_usage_percent > rule['threshold']
        elif rule['metric'] == 'memory_usage':
            return metrics.memory_usage_percent > rule['threshold']
        # ... 其他规则
        
    async def trigger_alert(self, rule: dict, metrics: SystemMonitorModel):
        """触发告警"""
        notification = NotificationHistory(
            notification_type='system_alert',
            title=f"系统告警: {rule['name']}",
            content=f"指标 {rule['metric']} 超过阈值 {rule['threshold']}%",
            priority=rule['priority'],
            delivery_method='desktop'
        )
        
        await self.notification_service.send_notification(notification)
```

### 4. 通知系统

#### 通知服务
```python
class NotificationService:
    """通知服务"""
    
    def __init__(self):
        self.handlers = {
            'desktop': DesktopNotificationHandler(),
            'email': EmailNotificationHandler(),
            'webhook': WebhookNotificationHandler()
        }
        
    async def send_notification(self, notification: NotificationHistory):
        """发送通知"""
        handler = self.handlers.get(notification.delivery_method)
        if not handler:
            raise ValueError(f"不支持的通知方式: {notification.delivery_method}")
            
        try:
            await handler.send(notification)
            notification.status = 'sent'
            notification.delivery_time = datetime.now()
            
        except Exception as e:
            notification.status = 'failed'
            notification.error_message = str(e)
            
            # 重试机制
            if notification.retry_count < notification.max_retries:
                await self.schedule_retry(notification)
                
        finally:
            self.notification_dao.update(notification)
```

## 🔄 数据流架构

### 任务执行流程
```
用户创建任务 → TaskService.create_task()
     ↓
任务入队 → TaskQueueManager.enqueue_task()
     ↓
调度器触发 → TaskScheduler.execute_task()
     ↓
脚本执行 → ScriptExecutor.execute_script()
     ↓
监控收集 → ResourceMonitor.collect_metrics()
     ↓
结果记录 → ExecutionDAO.update()
     ↓
通知发送 → NotificationService.send_notification()
```

### 事件驱动架构
```python
class EventBus:
    """事件总线"""
    
    def __init__(self):
        self.subscribers = defaultdict(list)
        
    def subscribe(self, event_type: str, handler: callable):
        """订阅事件"""
        self.subscribers[event_type].append(handler)
        
    async def publish(self, event_type: str, data: dict):
        """发布事件"""
        for handler in self.subscribers[event_type]:
            try:
                await handler(data)
            except Exception as e:
                logger.error(f"事件处理失败: {e}")

# 事件类型定义
EVENTS = {
    'task.created': 'task_created',
    'task.started': 'task_started',
    'task.completed': 'task_completed',
    'task.failed': 'task_failed',
    'system.alert': 'system_alert'
}
```

## 📊 性能优化策略

### 数据库优化
- **连接池**: SQLAlchemy连接池管理
- **索引优化**: 基于查询模式的复合索引
- **查询优化**: 延迟加载和批量操作
- **数据分区**: 按时间分区历史数据

### 内存优化
- **对象池**: 重用频繁创建的对象
- **缓存策略**: 多层缓存减少数据库访问
- **内存监控**: 实时监控内存使用情况
- **垃圾回收**: 主动释放不需要的资源

### 并发优化
- **异步处理**: asyncio异步IO操作
- **线程池**: 合理配置线程池大小
- **队列管理**: 优先级队列和负载均衡
- **资源限制**: 防止资源过度消耗

## 🛡️ 安全架构

### 脚本安全
- **沙箱执行**: RestrictedPython安全环境
- **权限控制**: 基于配置的权限管理
- **资源限制**: CPU、内存、时间限制
- **代码验证**: 脚本内容完整性检查

### 数据安全
- **敏感数据加密**: 配置信息加密存储
- **访问控制**: 数据访问权限控制
- **审计日志**: 完整的操作审计记录
- **备份恢复**: 定期数据备份和恢复

## 📈 可扩展性设计

### 插件架构
```python
class PluginManager:
    """插件管理器"""
    
    def __init__(self):
        self.plugins = {}
        
    def load_plugin(self, plugin_path: str):
        """加载插件"""
        spec = importlib.util.spec_from_file_location("plugin", plugin_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        if hasattr(module, 'Plugin'):
            plugin = module.Plugin()
            self.plugins[plugin.name] = plugin
            
    def execute_plugin(self, plugin_name: str, *args, **kwargs):
        """执行插件"""
        plugin = self.plugins.get(plugin_name)
        if plugin:
            return plugin.execute(*args, **kwargs)
```

### 微服务化准备
- **服务边界**: 清晰的服务职责划分
- **API设计**: RESTful API接口设计
- **消息队列**: 异步消息处理机制
- **配置中心**: 集中化配置管理

## 🔧 开发和部署

### 开发环境
- **虚拟环境**: venv隔离依赖
- **代码质量**: flake8 + black + mypy
- **测试框架**: pytest + pytest-qt
- **文档生成**: Sphinx自动文档

### 打包部署
- **应用打包**: PyInstaller单文件打包
- **安装程序**: NSIS Windows安装包
- **自动更新**: 内置更新检查机制
- **配置管理**: 用户配置和系统配置分离

## 📁 项目目录结构

### 推荐的项目组织结构
```
task_manager/
├── src/                          # 源代码目录
│   ├── __init__.py
│   ├── main.py                   # 应用程序入口
│   ├── config/                   # 配置管理
│   │   ├── __init__.py
│   │   ├── settings.py           # 应用配置
│   │   ├── database.py           # 数据库配置
│   │   └── logging.py            # 日志配置
│   ├── models/                   # ORM模型
│   │   ├── __init__.py
│   │   ├── base.py               # 基础模型类
│   │   ├── task.py               # 任务模型
│   │   ├── script.py             # 脚本模型
│   │   ├── execution.py          # 执行记录模型
│   │   ├── queue.py              # 队列模型
│   │   ├── notification.py       # 通知模型
│   │   └── monitor.py            # 监控模型
│   ├── dao/                      # 数据访问层
│   │   ├── __init__.py
│   │   ├── base_dao.py           # 基础DAO类
│   │   ├── task_dao.py           # 任务DAO
│   │   ├── script_dao.py         # 脚本DAO
│   │   ├── execution_dao.py      # 执行DAO
│   │   └── ...                   # 其他DAO类
│   ├── services/                 # 业务服务层
│   │   ├── __init__.py
│   │   ├── task_service.py       # 任务服务
│   │   ├── script_service.py     # 脚本服务
│   │   ├── execution_service.py  # 执行服务
│   │   ├── scheduler_service.py  # 调度服务
│   │   ├── notification_service.py # 通知服务
│   │   ├── monitor_service.py    # 监控服务
│   │   └── security_service.py   # 安全服务
│   ├── core/                     # 核心组件
│   │   ├── __init__.py
│   │   ├── scheduler.py          # 任务调度器
│   │   ├── executor.py           # 脚本执行器
│   │   ├── queue_manager.py      # 队列管理器
│   │   ├── event_bus.py          # 事件总线
│   │   ├── plugin_manager.py     # 插件管理器
│   │   └── security.py           # 安全组件
│   ├── ui/                       # 用户界面
│   │   ├── __init__.py
│   │   ├── main_window.py        # 主窗口
│   │   ├── widgets/              # UI组件
│   │   │   ├── __init__.py
│   │   │   ├── base_widget.py    # 基础组件
│   │   │   ├── task_widget.py    # 任务管理组件
│   │   │   ├── script_widget.py  # 脚本管理组件
│   │   │   ├── monitor_widget.py # 监控组件
│   │   │   └── settings_widget.py # 设置组件
│   │   ├── dialogs/              # 对话框
│   │   │   ├── __init__.py
│   │   │   ├── task_dialog.py    # 任务编辑对话框
│   │   │   ├── script_dialog.py  # 脚本编辑对话框
│   │   │   └── about_dialog.py   # 关于对话框
│   │   └── resources/            # UI资源
│   │       ├── icons/            # 图标文件
│   │       ├── styles/           # 样式文件
│   │       └── ui/               # UI文件
│   ├── utils/                    # 工具类
│   │   ├── __init__.py
│   │   ├── logger.py             # 日志工具
│   │   ├── crypto.py             # 加密工具
│   │   ├── file_utils.py         # 文件工具
│   │   ├── time_utils.py         # 时间工具
│   │   └── validation.py         # 验证工具
│   └── exceptions/               # 异常定义
│       ├── __init__.py
│       ├── base_exception.py     # 基础异常
│       ├── dao_exception.py      # DAO异常
│       ├── service_exception.py  # 服务异常
│       └── ui_exception.py       # UI异常
├── tests/                        # 测试代码
│   ├── __init__.py
│   ├── conftest.py               # pytest配置
│   ├── unit/                     # 单元测试
│   │   ├── test_models.py
│   │   ├── test_dao.py
│   │   ├── test_services.py
│   │   └── test_core.py
│   ├── integration/              # 集成测试
│   │   ├── test_task_flow.py
│   │   ├── test_execution.py
│   │   └── test_notification.py
│   └── ui/                       # UI测试
│       ├── test_main_window.py
│       ├── test_task_widget.py
│       └── test_script_widget.py
├── docs/                         # 文档
│   ├── 需求设计说明书.md
│   ├── 数据库模型设计.md
│   ├── 技术架构设计.md
│   ├── API文档.md
│   └── 用户手册.md
├── scripts/                      # 脚本文件
│   ├── init_db.py                # 数据库初始化
│   ├── migrate_db.py             # 数据库迁移
│   ├── backup_db.py              # 数据库备份
│   └── cleanup.py                # 数据清理
├── resources/                    # 资源文件
│   ├── icons/                    # 应用图标
│   ├── templates/                # 模板文件
│   └── sql/                      # SQL脚本
├── requirements.txt              # 依赖列表
├── requirements-dev.txt          # 开发依赖
├── setup.py                      # 安装脚本
├── pyproject.toml               # 项目配置
├── .gitignore                   # Git忽略文件
├── .flake8                      # 代码检查配置
├── pytest.ini                  # 测试配置
└── README.md                    # 项目说明
```

## 🔌 技术组件集成

### 依赖管理配置

#### requirements.txt
```txt
# 核心依赖
PyQt6>=6.5.0
PyQtChart>=6.5.0
QScintilla>=2.13.0
SQLAlchemy>=2.0.0
APScheduler>=3.10.0
psutil>=5.9.0
loguru>=0.7.0
cryptography>=41.0.0
requests>=2.31.0
pydantic>=2.0.0

# 安全组件
RestrictedPython>=6.0.0

# 通知组件
win10toast>=0.9.0
plyer>=2.1.0

# 缓存组件
diskcache>=5.6.0

# 开发工具
pytest>=7.4.0
pytest-qt>=4.2.0
pytest-cov>=4.1.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0
```

#### pyproject.toml
```toml
[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "task-manager"
version = "1.0.0"
description = "自动化任务管理工具"
authors = [{name = "开发团队", email = "<EMAIL>"}]
license = {text = "MIT"}
requires-python = ">=3.10"
dependencies = [
    "PyQt6>=6.5.0",
    "SQLAlchemy>=2.0.0",
    "APScheduler>=3.10.0",
    "psutil>=5.9.0",
    "loguru>=0.7.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-qt>=4.2.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
]

[tool.black]
line-length = 88
target-version = ['py38']

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short --strict-markers"
markers = [
    "slow: marks tests as slow",
    "integration: marks tests as integration tests",
    "ui: marks tests as UI tests",
]
```

## 🚀 部署和运维

### 应用打包配置

#### PyInstaller配置 (build.spec)
```python
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['src/main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('resources', 'resources'),
        ('src/ui/resources', 'ui/resources'),
    ],
    hiddenimports=[
        'PyQt6.QtCore',
        'PyQt6.QtGui',
        'PyQt6.QtWidgets',
        'PyQtChart.QtChart',
        'sqlalchemy.dialects.sqlite',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='TaskManager',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='resources/icons/app.ico'
)
```

### 配置管理

#### 应用配置 (src/config/settings.py)
```python
from pydantic import BaseSettings
from typing import Optional
import os

class Settings(BaseSettings):
    """应用配置"""

    # 应用基本信息
    app_name: str = "自动化任务管理工具"
    app_version: str = "1.0.0"
    debug: bool = False

    # 数据库配置
    database_url: str = "sqlite:///task_manager.db"
    database_echo: bool = False

    # 调度器配置
    scheduler_timezone: str = "Asia/Shanghai"
    max_workers: int = 3
    job_defaults: dict = {
        'coalesce': False,
        'max_instances': 1,
        'misfire_grace_time': 30
    }

    # 安全配置
    enable_sandbox: bool = True
    script_timeout: int = 3600
    max_memory_mb: int = 512

    # 日志配置
    log_level: str = "INFO"
    log_file: str = "logs/app.log"
    log_rotation: str = "1 day"
    log_retention: str = "30 days"

    # UI配置
    window_width: int = 1200
    window_height: int = 800
    theme: str = "default"

    # 通知配置
    enable_notifications: bool = True
    notification_timeout: int = 5000

    class Config:
        env_file = ".env"
        env_prefix = "TASK_MANAGER_"

# 全局配置实例
settings = Settings()
```

### 日志配置 (src/config/logging.py)
```python
from loguru import logger
import sys
import os
from .settings import settings

def setup_logging():
    """配置日志系统"""

    # 移除默认处理器
    logger.remove()

    # 控制台输出
    logger.add(
        sys.stdout,
        level=settings.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
               "<level>{message}</level>",
        colorize=True
    )

    # 文件输出
    os.makedirs(os.path.dirname(settings.log_file), exist_ok=True)
    logger.add(
        settings.log_file,
        level=settings.log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
        rotation=settings.log_rotation,
        retention=settings.log_retention,
        compression="zip",
        encoding="utf-8"
    )

    # 错误日志单独文件
    logger.add(
        "logs/error.log",
        level="ERROR",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
        rotation="1 day",
        retention="7 days",
        compression="zip",
        encoding="utf-8"
    )

# 初始化日志
setup_logging()
```

## 📊 监控和运维

### 健康检查
```python
class HealthChecker:
    """系统健康检查"""

    def __init__(self):
        self.checks = [
            self.check_database,
            self.check_scheduler,
            self.check_disk_space,
            self.check_memory_usage
        ]

    async def run_health_check(self) -> dict:
        """运行健康检查"""
        results = {}
        overall_status = "healthy"

        for check in self.checks:
            try:
                result = await check()
                results[check.__name__] = result
                if result['status'] != 'ok':
                    overall_status = "unhealthy"
            except Exception as e:
                results[check.__name__] = {
                    'status': 'error',
                    'message': str(e)
                }
                overall_status = "unhealthy"

        return {
            'overall_status': overall_status,
            'timestamp': datetime.now().isoformat(),
            'checks': results
        }
```

### 性能监控
```python
class PerformanceMonitor:
    """性能监控"""

    def __init__(self):
        self.metrics = {}

    def record_execution_time(self, operation: str, duration: float):
        """记录执行时间"""
        if operation not in self.metrics:
            self.metrics[operation] = []
        self.metrics[operation].append(duration)

    def get_performance_report(self) -> dict:
        """获取性能报告"""
        report = {}
        for operation, times in self.metrics.items():
            report[operation] = {
                'count': len(times),
                'avg_time': sum(times) / len(times),
                'min_time': min(times),
                'max_time': max(times)
            }
        return report
```

这个技术架构设计为自动化任务管理工具提供了完整的技术实现方案，包括详细的项目结构、配置管理、部署方案和运维监控，确保系统的高性能、高可用性和可扩展性。
