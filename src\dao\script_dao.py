"""
脚本数据访问对象

提供脚本相关的数据访问操作。
"""

from typing import List, Optional, Dict, Any
from sqlalchemy import and_, or_, func
from sqlalchemy.orm import Session

from ..models.script import Script, ScriptType, ScriptStatus
from ..models.script_version import ScriptVersion
from ..utils.logger import get_logger
from .base_dao import BaseDAO
from .exceptions import ValidationError, EntityNotFoundError

logger = get_logger(__name__)


class ScriptDAO(BaseDAO[Script]):
    """脚本数据访问对象"""
    
    def __init__(self):
        """初始化ScriptDAO"""
        super().__init__(Script)
    
    def get_by_name(self, name: str) -> Optional[Script]:
        """
        根据名称获取脚本
        
        Args:
            name: 脚本名称
            
        Returns:
            脚本实例或None
        """
        try:
            with self.get_session() as session:
                return session.query(Script).filter(
                    and_(
                        Script.name == name,
                        Script.is_deleted == False
                    )
                ).first()
        except Exception as e:
            logger.error(f"Error getting script by name {name}: {e}")
            raise
    
    def get_by_type(self, script_type: ScriptType, 
                   include_deleted: bool = False) -> List[Script]:
        """
        根据类型获取脚本列表
        
        Args:
            script_type: 脚本类型
            include_deleted: 是否包含已删除的脚本
            
        Returns:
            脚本列表
        """
        try:
            with self.get_session() as session:
                query = session.query(Script).filter(Script.script_type == script_type)
                
                if not include_deleted:
                    query = query.filter(Script.is_deleted == False)
                
                return query.order_by(Script.name).all()
        except Exception as e:
            logger.error(f"Error getting scripts by type {script_type}: {e}")
            raise
    
    def get_by_status(self, status: ScriptStatus, 
                     include_deleted: bool = False) -> List[Script]:
        """
        根据状态获取脚本列表
        
        Args:
            status: 脚本状态
            include_deleted: 是否包含已删除的脚本
            
        Returns:
            脚本列表
        """
        try:
            with self.get_session() as session:
                query = session.query(Script).filter(Script.status == status)
                
                if not include_deleted:
                    query = query.filter(Script.is_deleted == False)
                
                return query.order_by(Script.updated_at.desc()).all()
        except Exception as e:
            logger.error(f"Error getting scripts by status {status}: {e}")
            raise
    
    def get_by_category(self, category: str, 
                       include_deleted: bool = False) -> List[Script]:
        """
        根据分类获取脚本列表
        
        Args:
            category: 脚本分类
            include_deleted: 是否包含已删除的脚本
            
        Returns:
            脚本列表
        """
        try:
            with self.get_session() as session:
                query = session.query(Script).filter(Script.category == category)
                
                if not include_deleted:
                    query = query.filter(Script.is_deleted == False)
                
                return query.order_by(Script.name).all()
        except Exception as e:
            logger.error(f"Error getting scripts by category {category}: {e}")
            raise
    
    def search_by_keyword(self, keyword: str, 
                         include_deleted: bool = False) -> List[Script]:
        """
        根据关键词搜索脚本
        
        Args:
            keyword: 搜索关键词
            include_deleted: 是否包含已删除的脚本
            
        Returns:
            脚本列表
        """
        try:
            with self.get_session() as session:
                search_pattern = f"%{keyword}%"
                query = session.query(Script).filter(
                    or_(
                        Script.name.ilike(search_pattern),
                        Script.description.ilike(search_pattern),
                        Script.category.ilike(search_pattern)
                    )
                )
                
                if not include_deleted:
                    query = query.filter(Script.is_deleted == False)
                
                return query.order_by(Script.name).all()
        except Exception as e:
            logger.error(f"Error searching scripts by keyword {keyword}: {e}")
            raise
    
    def get_by_tags(self, tags: List[str], 
                   match_all: bool = False,
                   include_deleted: bool = False) -> List[Script]:
        """
        根据标签获取脚本列表
        
        Args:
            tags: 标签列表
            match_all: 是否匹配所有标签（True）或任意标签（False）
            include_deleted: 是否包含已删除的脚本
            
        Returns:
            脚本列表
        """
        try:
            with self.get_session() as session:
                query = session.query(Script)
                
                if match_all:
                    # 匹配所有标签
                    for tag in tags:
                        query = query.filter(Script.tags.contains([tag]))
                else:
                    # 匹配任意标签
                    tag_conditions = []
                    for tag in tags:
                        tag_conditions.append(Script.tags.contains([tag]))
                    query = query.filter(or_(*tag_conditions))
                
                if not include_deleted:
                    query = query.filter(Script.is_deleted == False)
                
                return query.order_by(Script.name).all()
        except Exception as e:
            logger.error(f"Error getting scripts by tags {tags}: {e}")
            raise
    
    def get_active_scripts(self) -> List[Script]:
        """
        获取所有活跃的脚本
        
        Returns:
            活跃脚本列表
        """
        return self.get_by_status(ScriptStatus.ACTIVE)
    
    def get_executable_scripts(self) -> List[Script]:
        """
        获取所有可执行的脚本
        
        Returns:
            可执行脚本列表
        """
        try:
            with self.get_session() as session:
                scripts = session.query(Script).filter(
                    and_(
                        Script.status == ScriptStatus.ACTIVE,
                        Script.is_deleted == False,
                        or_(
                            Script.content.isnot(None),
                            Script.file_path.isnot(None)
                        )
                    )
                ).order_by(Script.name).all()
                
                return scripts
        except Exception as e:
            logger.error(f"Error getting executable scripts: {e}")
            raise
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取脚本统计信息
        
        Returns:
            统计信息字典
        """
        try:
            with self.get_session() as session:
                # 总数统计
                total_count = session.query(Script).filter(Script.is_deleted == False).count()
                
                # 按状态统计
                status_stats = {}
                for status in ScriptStatus:
                    count = session.query(Script).filter(
                        and_(
                            Script.status == status,
                            Script.is_deleted == False
                        )
                    ).count()
                    status_stats[status.value] = count
                
                # 按类型统计
                type_stats = {}
                for script_type in ScriptType:
                    count = session.query(Script).filter(
                        and_(
                            Script.script_type == script_type,
                            Script.is_deleted == False
                        )
                    ).count()
                    type_stats[script_type.value] = count
                
                # 执行统计
                total_executions = session.query(func.sum(Script.execution_count)).filter(
                    Script.is_deleted == False
                ).scalar() or 0
                
                total_successes = session.query(func.sum(Script.success_count)).filter(
                    Script.is_deleted == False
                ).scalar() or 0
                
                # 计算平均成功率
                avg_success_rate = 0.0
                if total_executions > 0:
                    avg_success_rate = (total_successes / total_executions) * 100
                
                return {
                    "total_count": total_count,
                    "status_stats": status_stats,
                    "type_stats": type_stats,
                    "total_executions": total_executions,
                    "total_successes": total_successes,
                    "avg_success_rate": round(avg_success_rate, 2)
                }
        except Exception as e:
            logger.error(f"Error getting script statistics: {e}")
            raise
    
    def increment_execution_count(self, script_id: int, success: bool = True) -> None:
        """
        增加脚本执行计数
        
        Args:
            script_id: 脚本ID
            success: 是否执行成功
        """
        try:
            with self.get_session() as session:
                script = session.query(Script).filter(Script.id == script_id).first()
                if script:
                    script.increment_execution_count(success)
                    session.flush()
                    logger.debug(f"Incremented execution count for script {script_id}")
                else:
                    raise EntityNotFoundError("Script", script_id)
        except EntityNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Error incrementing execution count for script {script_id}: {e}")
            raise
    
    def update_status(self, script_id: int, status: ScriptStatus) -> Script:
        """
        更新脚本状态
        
        Args:
            script_id: 脚本ID
            status: 新状态
            
        Returns:
            更新后的脚本实例
        """
        return self.update(script_id, status=status)
    
    def add_tag(self, script_id: int, tag: str) -> Script:
        """
        为脚本添加标签
        
        Args:
            script_id: 脚本ID
            tag: 标签
            
        Returns:
            更新后的脚本实例
        """
        try:
            with self.get_session() as session:
                script = session.query(Script).filter(Script.id == script_id).first()
                if script:
                    script.add_tag(tag)
                    session.flush()
                    session.refresh(script)
                    session.expunge(script)
                    logger.debug(f"Added tag '{tag}' to script {script_id}")
                    return script
                else:
                    raise EntityNotFoundError("Script", script_id)
        except EntityNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Error adding tag to script {script_id}: {e}")
            raise
    
    def remove_tag(self, script_id: int, tag: str) -> Script:
        """
        从脚本移除标签
        
        Args:
            script_id: 脚本ID
            tag: 标签
            
        Returns:
            更新后的脚本实例
        """
        try:
            with self.get_session() as session:
                script = session.query(Script).filter(Script.id == script_id).first()
                if script:
                    script.remove_tag(tag)
                    session.flush()
                    session.refresh(script)
                    session.expunge(script)
                    logger.debug(f"Removed tag '{tag}' from script {script_id}")
                    return script
                else:
                    raise EntityNotFoundError("Script", script_id)
        except EntityNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Error removing tag from script {script_id}: {e}")
            raise
    
    def _validate_create_data(self, data: Dict[str, Any]) -> None:
        """
        验证脚本创建数据
        
        Args:
            data: 创建数据
            
        Raises:
            ValidationError: 验证失败
        """
        # 验证必需字段
        if not data.get('name'):
            raise ValidationError('name', data.get('name'), 'Name is required')
        
        if not data.get('script_type'):
            raise ValidationError('script_type', data.get('script_type'), 'Script type is required')
        
        # 验证名称唯一性
        if self.get_by_name(data['name']):
            raise ValidationError('name', data['name'], 'Script name already exists')
        
        # 验证脚本内容
        if not data.get('content') and not data.get('file_path'):
            raise ValidationError('content', None, 'Either content or file_path is required')
    
    def _validate_update_data(self, instance: Script, data: Dict[str, Any]) -> None:
        """
        验证脚本更新数据
        
        Args:
            instance: 现有脚本实例
            data: 更新数据
            
        Raises:
            ValidationError: 验证失败
        """
        # 验证名称唯一性（如果更新名称）
        if 'name' in data and data['name'] != instance.name:
            existing = self.get_by_name(data['name'])
            if existing and existing.id != instance.id:
                raise ValidationError('name', data['name'], 'Script name already exists')
