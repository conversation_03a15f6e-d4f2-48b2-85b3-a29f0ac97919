"""
任务队列数据访问对象

提供任务队列相关的数据访问操作。
"""

from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from sqlalchemy import and_, or_, desc

from ..models.task_queue import TaskQueue, QueueStatus
from ..utils.logger import get_logger
from .base_dao import BaseDAO
from .exceptions import ValidationError, EntityNotFoundError

logger = get_logger(__name__)


class TaskQueueDAO(BaseDAO[TaskQueue]):
    """任务队列数据访问对象"""
    
    def __init__(self):
        """初始化TaskQueueDAO"""
        super().__init__(TaskQueue)
    
    def get_by_task_id(self, task_id: int) -> List[TaskQueue]:
        """
        根据任务ID获取队列项列表
        
        Args:
            task_id: 任务ID
            
        Returns:
            队列项列表
        """
        try:
            with self.get_session() as session:
                return session.query(TaskQueue).filter(
                    TaskQueue.task_id == task_id
                ).order_by(desc(TaskQueue.created_at)).all()
        except Exception as e:
            logger.error(f"Error getting queue items by task_id {task_id}: {e}")
            raise
    
    def get_by_status(self, status: QueueStatus, 
                     queue_name: Optional[str] = None) -> List[TaskQueue]:
        """
        根据状态获取队列项列表
        
        Args:
            status: 队列状态
            queue_name: 队列名称（可选）
            
        Returns:
            队列项列表
        """
        try:
            with self.get_session() as session:
                query = session.query(TaskQueue).filter(TaskQueue.status == status)
                
                if queue_name:
                    query = query.filter(TaskQueue.queue_name == queue_name)
                
                return query.order_by(
                    desc(TaskQueue.priority),
                    TaskQueue.scheduled_time,
                    TaskQueue.created_at
                ).all()
        except Exception as e:
            logger.error(f"Error getting queue items by status {status}: {e}")
            raise
    
    def get_by_queue_name(self, queue_name: str) -> List[TaskQueue]:
        """
        根据队列名称获取队列项列表
        
        Args:
            queue_name: 队列名称
            
        Returns:
            队列项列表
        """
        try:
            with self.get_session() as session:
                return session.query(TaskQueue).filter(
                    TaskQueue.queue_name == queue_name
                ).order_by(
                    desc(TaskQueue.priority),
                    TaskQueue.scheduled_time,
                    TaskQueue.created_at
                ).all()
        except Exception as e:
            logger.error(f"Error getting queue items by queue_name {queue_name}: {e}")
            raise
    
    def get_pending_items(self, queue_name: Optional[str] = None, 
                         limit: Optional[int] = None) -> List[TaskQueue]:
        """
        获取待处理的队列项
        
        Args:
            queue_name: 队列名称（可选）
            limit: 限制返回数量
            
        Returns:
            待处理队列项列表
        """
        try:
            with self.get_session() as session:
                now = datetime.now()
                
                query = session.query(TaskQueue).filter(
                    and_(
                        TaskQueue.status == QueueStatus.PENDING,
                        or_(
                            TaskQueue.scheduled_time.is_(None),
                            TaskQueue.scheduled_time <= now
                        ),
                        or_(
                            TaskQueue.next_retry_time.is_(None),
                            TaskQueue.next_retry_time <= now
                        )
                    )
                )
                
                if queue_name:
                    query = query.filter(TaskQueue.queue_name == queue_name)
                
                query = query.order_by(
                    desc(TaskQueue.priority),
                    TaskQueue.scheduled_time,
                    TaskQueue.created_at
                )
                
                if limit:
                    query = query.limit(limit)
                
                return query.all()
        except Exception as e:
            logger.error(f"Error getting pending queue items: {e}")
            raise
    
    def get_processing_items(self, worker_id: Optional[str] = None) -> List[TaskQueue]:
        """
        获取正在处理的队列项
        
        Args:
            worker_id: 工作器ID（可选）
            
        Returns:
            正在处理的队列项列表
        """
        try:
            with self.get_session() as session:
                query = session.query(TaskQueue).filter(
                    TaskQueue.status == QueueStatus.PROCESSING
                )
                
                if worker_id:
                    query = query.filter(TaskQueue.worker_id == worker_id)
                
                return query.order_by(TaskQueue.started_time).all()
        except Exception as e:
            logger.error(f"Error getting processing queue items: {e}")
            raise
    
    def get_failed_items(self, can_retry: bool = True) -> List[TaskQueue]:
        """
        获取失败的队列项
        
        Args:
            can_retry: 是否只返回可重试的项
            
        Returns:
            失败队列项列表
        """
        try:
            with self.get_session() as session:
                query = session.query(TaskQueue).filter(
                    TaskQueue.status == QueueStatus.FAILED
                )
                
                if can_retry:
                    # 只返回可重试的项
                    items = query.all()
                    return [item for item in items if item.can_retry]
                else:
                    return query.order_by(desc(TaskQueue.completed_time)).all()
        except Exception as e:
            logger.error(f"Error getting failed queue items: {e}")
            raise
    
    def get_ready_for_processing(self, queue_name: Optional[str] = None,
                                limit: Optional[int] = None) -> List[TaskQueue]:
        """
        获取准备处理的队列项
        
        Args:
            queue_name: 队列名称（可选）
            limit: 限制返回数量
            
        Returns:
            准备处理的队列项列表
        """
        try:
            with self.get_session() as session:
                query = session.query(TaskQueue)
                
                if queue_name:
                    query = query.filter(TaskQueue.queue_name == queue_name)
                
                # 获取所有项并过滤
                items = query.all()
                ready_items = [item for item in items if item.is_ready_for_processing]
                
                # 按优先级和时间排序
                ready_items.sort(key=lambda x: (-x.priority, x.scheduled_time or x.created_at))
                
                if limit:
                    ready_items = ready_items[:limit]
                
                return ready_items
        except Exception as e:
            logger.error(f"Error getting ready queue items: {e}")
            raise
    
    def enqueue_task(self, task_id: int, queue_name: str = "default",
                    priority: int = 5, scheduled_time: Optional[datetime] = None) -> TaskQueue:
        """
        将任务加入队列
        
        Args:
            task_id: 任务ID
            queue_name: 队列名称
            priority: 优先级
            scheduled_time: 计划执行时间
            
        Returns:
            创建的队列项
        """
        queue_data = {
            "task_id": task_id,
            "queue_name": queue_name,
            "priority": priority,
            "scheduled_time": scheduled_time,
            "status": QueueStatus.PENDING
        }
        
        queue_item = self.create(**queue_data)
        logger.debug(f"Enqueued task {task_id} to queue {queue_name}")
        return queue_item
    
    def start_processing(self, queue_item_id: int, worker_id: str, 
                        execution_id: str) -> TaskQueue:
        """
        开始处理队列项
        
        Args:
            queue_item_id: 队列项ID
            worker_id: 工作器ID
            execution_id: 执行ID
            
        Returns:
            更新后的队列项
        """
        try:
            with self.get_session() as session:
                queue_item = session.query(TaskQueue).filter(
                    TaskQueue.id == queue_item_id
                ).first()
                
                if queue_item:
                    queue_item.start_processing(worker_id, execution_id)
                    session.flush()
                    session.refresh(queue_item)
                    logger.debug(f"Started processing queue item {queue_item_id}")
                    return queue_item
                else:
                    raise EntityNotFoundError("TaskQueue", queue_item_id)
        except EntityNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Error starting processing queue item: {e}")
            raise
    
    def complete_processing(self, queue_item_id: int, success: bool = True,
                           error_message: Optional[str] = None) -> TaskQueue:
        """
        完成处理队列项
        
        Args:
            queue_item_id: 队列项ID
            success: 是否成功
            error_message: 错误消息
            
        Returns:
            更新后的队列项
        """
        try:
            with self.get_session() as session:
                queue_item = session.query(TaskQueue).filter(
                    TaskQueue.id == queue_item_id
                ).first()
                
                if queue_item:
                    queue_item.complete_processing(success, error_message)
                    session.flush()
                    session.refresh(queue_item)
                    logger.debug(f"Completed processing queue item {queue_item_id}")
                    return queue_item
                else:
                    raise EntityNotFoundError("TaskQueue", queue_item_id)
        except EntityNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Error completing processing queue item: {e}")
            raise
    
    def schedule_retry(self, queue_item_id: int, retry_delay: int = 60) -> bool:
        """
        安排重试
        
        Args:
            queue_item_id: 队列项ID
            retry_delay: 重试延迟（秒）
            
        Returns:
            是否安排成功
        """
        try:
            with self.get_session() as session:
                queue_item = session.query(TaskQueue).filter(
                    TaskQueue.id == queue_item_id
                ).first()
                
                if queue_item:
                    success = queue_item.schedule_retry(retry_delay)
                    if success:
                        session.flush()
                        logger.debug(f"Scheduled retry for queue item {queue_item_id}")
                    return success
                else:
                    raise EntityNotFoundError("TaskQueue", queue_item_id)
        except EntityNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Error scheduling retry for queue item: {e}")
            raise
    
    def get_statistics(self, queue_name: Optional[str] = None) -> Dict[str, Any]:
        """
        获取队列统计信息
        
        Args:
            queue_name: 队列名称（可选）
            
        Returns:
            统计信息字典
        """
        try:
            with self.get_session() as session:
                query = session.query(TaskQueue)
                
                if queue_name:
                    query = query.filter(TaskQueue.queue_name == queue_name)
                
                # 按状态统计
                status_stats = {}
                for status in QueueStatus:
                    count = query.filter(TaskQueue.status == status).count()
                    status_stats[status.value] = count
                
                # 总数
                total_count = query.count()
                
                # 平均处理时间
                completed_items = query.filter(
                    TaskQueue.status == QueueStatus.COMPLETED
                ).all()
                
                avg_processing_time = 0.0
                if completed_items:
                    total_time = sum(item.processing_duration or 0 for item in completed_items)
                    avg_processing_time = total_time / len(completed_items)
                
                return {
                    "queue_name": queue_name or "all",
                    "total_count": total_count,
                    "status_stats": status_stats,
                    "avg_processing_time": round(avg_processing_time, 2)
                }
        except Exception as e:
            logger.error(f"Error getting queue statistics: {e}")
            raise
    
    def _validate_create_data(self, data: Dict[str, Any]) -> None:
        """
        验证队列项创建数据
        
        Args:
            data: 创建数据
            
        Raises:
            ValidationError: 验证失败
        """
        # 验证必需字段
        if not data.get('task_id'):
            raise ValidationError('task_id', data.get('task_id'), 'Task ID is required')
        
        # 验证任务存在性
        from .task_dao import TaskDAO
        task_dao = TaskDAO()
        if not task_dao.exists(data['task_id']):
            raise ValidationError('task_id', data['task_id'], 'Task does not exist')
