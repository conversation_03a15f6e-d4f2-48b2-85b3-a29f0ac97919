"""
配置管理模块

提供应用程序配置的加载、保存、验证和热更新功能。
"""

import json
import os
from pathlib import Path
from typing import Any, Dict, Optional, Union, List
import threading
from dataclasses import dataclass, asdict
from datetime import datetime

from .settings import get_settings, CONFIG_DIR
from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class ConfigChangeEvent:
    """配置变更事件"""
    key: str
    old_value: Any
    new_value: Any
    timestamp: datetime


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "app_config.json"):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件名
        """
        self.config_file = Path(CONFIG_DIR) / config_file
        self.config_file.parent.mkdir(parents=True, exist_ok=True)
        
        self._config: Dict[str, Any] = {}
        self._default_config: Dict[str, Any] = get_settings()
        self._lock = threading.RLock()
        self._change_listeners: List[callable] = []
        
        self.load_config()
    
    def load_config(self) -> None:
        """加载配置文件"""
        with self._lock:
            try:
                if self.config_file.exists():
                    with open(self.config_file, 'r', encoding='utf-8') as f:
                        user_config = json.load(f)
                    
                    # 合并默认配置和用户配置
                    self._config = self._merge_configs(self._default_config, user_config)
                    logger.info(f"配置文件加载成功: {self.config_file}")
                else:
                    # 使用默认配置
                    self._config = self._default_config.copy()
                    self.save_config()
                    logger.info("使用默认配置并创建配置文件")
                
                # 验证配置
                self._validate_config()
                
            except Exception as e:
                logger.error(f"加载配置文件失败: {e}")
                self._config = self._default_config.copy()
    
    def save_config(self) -> bool:
        """保存配置到文件"""
        with self._lock:
            try:
                # 只保存与默认配置不同的部分
                user_config = self._get_user_config()
                
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(user_config, f, indent=2, ensure_ascii=False)
                
                logger.info(f"配置文件保存成功: {self.config_file}")
                return True
                
            except Exception as e:
                logger.error(f"保存配置文件失败: {e}")
                return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键（如 'ui.theme.default'）
            default: 默认值
            
        Returns:
            配置值
        """
        with self._lock:
            try:
                keys = key.split('.')
                value = self._config
                
                for k in keys:
                    if isinstance(value, dict) and k in value:
                        value = value[k]
                    else:
                        return default
                
                return value
                
            except Exception as e:
                logger.warning(f"获取配置失败 {key}: {e}")
                return default
    
    def set(self, key: str, value: Any, save: bool = True) -> bool:
        """
        设置配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            value: 配置值
            save: 是否立即保存到文件
            
        Returns:
            是否设置成功
        """
        with self._lock:
            try:
                keys = key.split('.')
                config = self._config
                
                # 获取旧值用于事件通知
                old_value = self.get(key)
                
                # 导航到目标位置
                for k in keys[:-1]:
                    if k not in config:
                        config[k] = {}
                    config = config[k]
                
                # 设置值
                config[keys[-1]] = value
                
                # 验证配置
                if not self._validate_single_config(key, value):
                    logger.warning(f"配置值验证失败: {key}={value}")
                    return False
                
                # 触发变更事件
                self._notify_change(key, old_value, value)
                
                # 保存配置
                if save:
                    self.save_config()
                
                logger.debug(f"配置设置成功: {key}={value}")
                return True
                
            except Exception as e:
                logger.error(f"设置配置失败 {key}={value}: {e}")
                return False
    
    def delete(self, key: str, save: bool = True) -> bool:
        """
        删除配置项
        
        Args:
            key: 配置键
            save: 是否立即保存到文件
            
        Returns:
            是否删除成功
        """
        with self._lock:
            try:
                keys = key.split('.')
                config = self._config
                
                # 导航到父级
                for k in keys[:-1]:
                    if k not in config:
                        return False
                    config = config[k]
                
                # 删除配置项
                if keys[-1] in config:
                    old_value = config[keys[-1]]
                    del config[keys[-1]]
                    
                    # 触发变更事件
                    self._notify_change(key, old_value, None)
                    
                    # 保存配置
                    if save:
                        self.save_config()
                    
                    logger.debug(f"配置删除成功: {key}")
                    return True
                
                return False
                
            except Exception as e:
                logger.error(f"删除配置失败 {key}: {e}")
                return False
    
    def reset_to_default(self, key: Optional[str] = None) -> bool:
        """
        重置配置为默认值
        
        Args:
            key: 要重置的配置键，None表示重置所有配置
            
        Returns:
            是否重置成功
        """
        with self._lock:
            try:
                if key is None:
                    # 重置所有配置
                    self._config = self._default_config.copy()
                    logger.info("所有配置已重置为默认值")
                else:
                    # 重置指定配置
                    default_value = self._get_default_value(key)
                    if default_value is not None:
                        self.set(key, default_value, save=False)
                        logger.info(f"配置已重置为默认值: {key}")
                    else:
                        logger.warning(f"未找到默认配置: {key}")
                        return False
                
                self.save_config()
                return True
                
            except Exception as e:
                logger.error(f"重置配置失败: {e}")
                return False
    
    def add_change_listener(self, listener: callable) -> None:
        """
        添加配置变更监听器
        
        Args:
            listener: 监听器函数，接收 ConfigChangeEvent 参数
        """
        with self._lock:
            if listener not in self._change_listeners:
                self._change_listeners.append(listener)
                logger.debug(f"添加配置变更监听器: {listener.__name__}")
    
    def remove_change_listener(self, listener: callable) -> None:
        """
        移除配置变更监听器
        
        Args:
            listener: 要移除的监听器函数
        """
        with self._lock:
            if listener in self._change_listeners:
                self._change_listeners.remove(listener)
                logger.debug(f"移除配置变更监听器: {listener.__name__}")
    
    def get_all_config(self) -> Dict[str, Any]:
        """获取所有配置"""
        with self._lock:
            return self._config.copy()
    
    def _merge_configs(self, default: Dict[str, Any], user: Dict[str, Any]) -> Dict[str, Any]:
        """合并默认配置和用户配置"""
        result = default.copy()
        
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _get_user_config(self) -> Dict[str, Any]:
        """获取用户自定义的配置（与默认配置不同的部分）"""
        return self._get_diff_config(self._config, self._default_config)
    
    def _get_diff_config(self, current: Dict[str, Any], default: Dict[str, Any]) -> Dict[str, Any]:
        """获取两个配置的差异"""
        diff = {}
        
        for key, value in current.items():
            if key not in default:
                diff[key] = value
            elif isinstance(value, dict) and isinstance(default[key], dict):
                sub_diff = self._get_diff_config(value, default[key])
                if sub_diff:
                    diff[key] = sub_diff
            elif value != default[key]:
                diff[key] = value
        
        return diff
    
    def _get_default_value(self, key: str) -> Any:
        """获取指定键的默认值"""
        keys = key.split('.')
        value = self._default_config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return None
        
        return value
    
    def _validate_config(self) -> bool:
        """验证整个配置"""
        # 这里可以添加配置验证逻辑
        return True
    
    def _validate_single_config(self, key: str, value: Any) -> bool:
        """验证单个配置项"""
        # 这里可以添加特定配置项的验证逻辑
        return True
    
    def _notify_change(self, key: str, old_value: Any, new_value: Any) -> None:
        """通知配置变更"""
        event = ConfigChangeEvent(
            key=key,
            old_value=old_value,
            new_value=new_value,
            timestamp=datetime.now()
        )
        
        for listener in self._change_listeners:
            try:
                listener(event)
            except Exception as e:
                logger.error(f"配置变更监听器执行失败: {e}")


# 全局配置管理器实例
# 使用双重持久化配置管理器
try:
    from .dual_persistence_config_manager import DualPersistenceConfigManager
    config_manager = DualPersistenceConfigManager()
except ImportError as e:
    # 如果导入失败，回退到标准配置管理器
    import logging
    logging.getLogger(__name__).warning(f"无法导入双重持久化配置管理器，使用标准配置管理器: {e}")
    config_manager = ConfigManager()

# 便捷函数
def get_config(key: str, default: Any = None) -> Any:
    """获取配置的便捷函数"""
    return config_manager.get(key, default)

def set_config(key: str, value: Any, save: bool = True) -> bool:
    """设置配置的便捷函数"""
    return config_manager.set(key, value, save)

def save_config() -> bool:
    """保存配置的便捷函数"""
    return config_manager.save_config()

def reset_config(key: Optional[str] = None) -> bool:
    """重置配置的便捷函数"""
    return config_manager.reset_to_default(key)

def get_config_manager() -> Union[ConfigManager, 'DualPersistenceConfigManager']:
    """获取配置管理器实例"""
    return config_manager
