"""
数据访问对象模块

提供完整的数据访问层实现，包括基础DAO类和具体的数据访问对象。
"""

# 异常类
from .exceptions import (
    DAOException, EntityNotFoundError, DuplicateEntityError,
    ValidationError, DatabaseConnectionError, TransactionError,
    QueryError, ConcurrencyError, PermissionError, DataIntegrityError
)

# 查询构建器
from .query_builder import QueryBuilder

# 基础DAO
from .base_dao import BaseDAO

# 具体DAO类
from .script_dao import ScriptDAO
from .task_dao import TaskDAO
from .execution_dao import ExecutionDAO
from .config_dao import ConfigDAO
from .script_version_dao import ScriptVersionDAO
from .task_queue_dao import TaskQueueDAO

# 导出所有类
__all__ = [
    # 异常类
    'DAOException',
    'EntityNotFoundError',
    'DuplicateEntityError',
    'ValidationError',
    'DatabaseConnectionError',
    'TransactionError',
    'QueryError',
    'ConcurrencyError',
    'PermissionError',
    'DataIntegrityError',

    # 工具类
    'QueryBuilder',

    # 基础类
    'BaseDAO',

    # 具体DAO类
    'ScriptDAO',
    'TaskDAO',
    'ExecutionDAO',
    'ConfigDAO',
    'ScriptVersionDAO',
    'TaskQueueDAO',
]

# DAO注册表
DAO_REGISTRY = {
    'Script': ScriptDAO,
    'Task': TaskDAO,
    'Execution': ExecutionDAO,
    'Config': ConfigDAO,
    'ScriptVersion': ScriptVersionDAO,
    'TaskQueue': TaskQueueDAO,
}


def get_dao_class(model_name: str):
    """
    根据模型名称获取DAO类

    Args:
        model_name: 模型名称

    Returns:
        DAO类或None
    """
    return DAO_REGISTRY.get(model_name)


def get_dao_instance(model_name: str):
    """
    根据模型名称获取DAO实例

    Args:
        model_name: 模型名称

    Returns:
        DAO实例或None
    """
    dao_class = get_dao_class(model_name)
    if dao_class:
        return dao_class()
    return None


def get_all_dao_classes():
    """
    获取所有DAO类

    Returns:
        DAO类列表
    """
    return list(DAO_REGISTRY.values())


def get_all_dao_instances():
    """
    获取所有DAO实例

    Returns:
        DAO实例字典
    """
    instances = {}
    for model_name, dao_class in DAO_REGISTRY.items():
        instances[model_name] = dao_class()
    return instances
