"""
查询构建器

提供复杂查询的构建工具。
"""

from typing import Any, Dict, List, Optional, Union, Type
from sqlalchemy import and_, or_, not_, desc, asc, func
from sqlalchemy.orm import Query, Session
from sqlalchemy.sql import operators

from ..models.base import BaseModel
from .exceptions import QueryError


class QueryBuilder:
    """查询构建器类"""
    
    def __init__(self, session: Session, model_class: Type[BaseModel]):
        """
        初始化查询构建器
        
        Args:
            session: 数据库会话
            model_class: 模型类
        """
        self.session = session
        self.model_class = model_class
        self.query = session.query(model_class)
        self._filters = []
        self._orders = []
        self._joins = []
    
    def filter(self, **kwargs) -> 'QueryBuilder':
        """
        添加过滤条件
        
        Args:
            **kwargs: 过滤条件
            
        Returns:
            查询构建器实例
        """
        for field, value in kwargs.items():
            if hasattr(self.model_class, field):
                column = getattr(self.model_class, field)
                self._filters.append(column == value)
        return self
    
    def filter_by_field(self, field: str, operator: str, value: Any) -> 'QueryBuilder':
        """
        按字段和操作符添加过滤条件
        
        Args:
            field: 字段名
            operator: 操作符 (eq, ne, gt, ge, lt, le, like, in, not_in)
            value: 值
            
        Returns:
            查询构建器实例
        """
        if not hasattr(self.model_class, field):
            raise QueryError("filter_by_field", {"field": field, "error": "Field not found"})
        
        column = getattr(self.model_class, field)
        
        if operator == "eq":
            self._filters.append(column == value)
        elif operator == "ne":
            self._filters.append(column != value)
        elif operator == "gt":
            self._filters.append(column > value)
        elif operator == "ge":
            self._filters.append(column >= value)
        elif operator == "lt":
            self._filters.append(column < value)
        elif operator == "le":
            self._filters.append(column <= value)
        elif operator == "like":
            self._filters.append(column.like(f"%{value}%"))
        elif operator == "ilike":
            self._filters.append(column.ilike(f"%{value}%"))
        elif operator == "in":
            self._filters.append(column.in_(value))
        elif operator == "not_in":
            self._filters.append(~column.in_(value))
        elif operator == "is_null":
            self._filters.append(column.is_(None))
        elif operator == "is_not_null":
            self._filters.append(column.isnot(None))
        else:
            raise QueryError("filter_by_field", {"operator": operator, "error": "Unsupported operator"})
        
        return self
    
    def filter_active(self) -> 'QueryBuilder':
        """
        过滤未删除的记录
        
        Returns:
            查询构建器实例
        """
        if hasattr(self.model_class, 'is_deleted'):
            self._filters.append(self.model_class.is_deleted == False)
        return self
    
    def search(self, keyword: str, fields: List[str]) -> 'QueryBuilder':
        """
        在指定字段中搜索关键词
        
        Args:
            keyword: 搜索关键词
            fields: 搜索字段列表
            
        Returns:
            查询构建器实例
        """
        if not keyword or not fields:
            return self
        
        search_conditions = []
        for field in fields:
            if hasattr(self.model_class, field):
                column = getattr(self.model_class, field)
                search_conditions.append(column.ilike(f"%{keyword}%"))
        
        if search_conditions:
            self._filters.append(or_(*search_conditions))
        
        return self
    
    def order_by(self, field: str, direction: str = "asc") -> 'QueryBuilder':
        """
        添加排序条件
        
        Args:
            field: 排序字段
            direction: 排序方向 (asc, desc)
            
        Returns:
            查询构建器实例
        """
        if not hasattr(self.model_class, field):
            raise QueryError("order_by", {"field": field, "error": "Field not found"})
        
        column = getattr(self.model_class, field)
        
        if direction.lower() == "desc":
            self._orders.append(desc(column))
        else:
            self._orders.append(asc(column))
        
        return self
    
    def join(self, *args) -> 'QueryBuilder':
        """
        添加JOIN操作
        
        Args:
            *args: JOIN参数
            
        Returns:
            查询构建器实例
        """
        self._joins.extend(args)
        return self
    
    def build(self) -> Query:
        """
        构建查询对象
        
        Returns:
            SQLAlchemy查询对象
        """
        query = self.query
        
        # 添加JOIN
        for join_arg in self._joins:
            query = query.join(join_arg)
        
        # 添加过滤条件
        if self._filters:
            query = query.filter(and_(*self._filters))
        
        # 添加排序
        if self._orders:
            query = query.order_by(*self._orders)
        
        return query
    
    def count(self) -> int:
        """
        获取记录数量
        
        Returns:
            记录数量
        """
        query = self.build()
        return query.count()
    
    def first(self) -> Optional[BaseModel]:
        """
        获取第一条记录
        
        Returns:
            模型实例或None
        """
        query = self.build()
        return query.first()
    
    def all(self) -> List[BaseModel]:
        """
        获取所有记录
        
        Returns:
            模型实例列表
        """
        query = self.build()
        return query.all()
    
    def paginate(self, page: int = 1, per_page: int = 20) -> Dict[str, Any]:
        """
        分页查询
        
        Args:
            page: 页码（从1开始）
            per_page: 每页记录数
            
        Returns:
            分页结果字典
        """
        if page < 1:
            page = 1
        if per_page < 1:
            per_page = 20
        
        query = self.build()
        
        # 计算总数
        total = query.count()
        
        # 计算偏移量
        offset = (page - 1) * per_page
        
        # 获取当前页数据
        items = query.offset(offset).limit(per_page).all()
        
        # 计算分页信息
        total_pages = (total + per_page - 1) // per_page
        has_prev = page > 1
        has_next = page < total_pages
        
        return {
            "items": items,
            "total": total,
            "page": page,
            "per_page": per_page,
            "total_pages": total_pages,
            "has_prev": has_prev,
            "has_next": has_next,
            "prev_page": page - 1 if has_prev else None,
            "next_page": page + 1 if has_next else None
        }
    
    def exists(self) -> bool:
        """
        检查是否存在匹配的记录
        
        Returns:
            是否存在
        """
        query = self.build()
        return query.first() is not None
    
    def aggregate(self, field: str, func_name: str) -> Any:
        """
        聚合查询
        
        Args:
            field: 聚合字段
            func_name: 聚合函数名 (count, sum, avg, min, max)
            
        Returns:
            聚合结果
        """
        if not hasattr(self.model_class, field):
            raise QueryError("aggregate", {"field": field, "error": "Field not found"})
        
        column = getattr(self.model_class, field)
        
        if func_name == "count":
            agg_func = func.count(column)
        elif func_name == "sum":
            agg_func = func.sum(column)
        elif func_name == "avg":
            agg_func = func.avg(column)
        elif func_name == "min":
            agg_func = func.min(column)
        elif func_name == "max":
            agg_func = func.max(column)
        else:
            raise QueryError("aggregate", {"func_name": func_name, "error": "Unsupported function"})
        
        # 构建基础查询（不包括排序）
        query = self.session.query(self.model_class)
        
        # 添加JOIN
        for join_arg in self._joins:
            query = query.join(join_arg)
        
        # 添加过滤条件
        if self._filters:
            query = query.filter(and_(*self._filters))
        
        # 执行聚合查询
        result = query.with_entities(agg_func).scalar()
        return result
