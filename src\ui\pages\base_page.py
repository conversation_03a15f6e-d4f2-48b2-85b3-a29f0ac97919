"""
页面基类

定义所有页面的基础接口和通用功能。
基于T2.1主窗口框架的高质量标准实现。
"""

from typing import Optional, Dict, Any
import logging
from abc import ABC, abstractmethod

from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel, QFrame
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont

from src.utils.logger import get_logger


class BasePageMeta(type(QWidget), type(ABC)):
    """解决元类冲突的元类"""
    pass


class BasePage(QWidget, ABC, metaclass=BasePageMeta):
    """
    页面基类
    
    定义所有页面的基础接口和通用功能。
    """
    
    # 信号定义
    page_loaded = pyqtSignal()
    page_error = pyqtSignal(str)
    status_message = pyqtSignal(str, int)  # (message, timeout)
    
    def __init__(self, parent: Optional[QWidget] = None):
        """
        初始化页面
        
        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        
        # 初始化日志
        self.logger = get_logger(self.__class__.__name__)
        
        # 页面状态
        self.is_loaded = False
        self.is_loading = False
        
        # 设置页面属性
        self._setup_page_properties()
        
        # 初始化UI
        self._setup_ui()
        
        self.logger.info(f"页面初始化完成: {self.get_page_title()}")
    
    def _setup_page_properties(self) -> None:
        """设置页面属性"""
        self.setObjectName(f"{self.__class__.__name__}_page")
    
    def _setup_ui(self) -> None:
        """设置UI（子类可重写）"""
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(16, 16, 16, 16)
        main_layout.setSpacing(16)
        
        # 创建页面头部
        # self._create_page_header(main_layout)
        
        # 创建页面内容
        self._create_page_content(main_layout)
    
    def _create_page_header(self, layout: QVBoxLayout) -> None:
        """
        创建页面头部
        
        Args:
            layout: 主布局
        """
        # 页面标题
        title_label = QLabel(self.get_page_title())
        title_label.setObjectName("page_title")
        
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        
        layout.addWidget(title_label)
        
        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        layout.addWidget(separator)
    
    @abstractmethod
    def _create_page_content(self, layout: QVBoxLayout) -> None:
        """
        创建页面内容（子类必须实现）
        
        Args:
            layout: 主布局
        """
        pass
    
    @abstractmethod
    def get_page_title(self) -> str:
        """
        获取页面标题（子类必须实现）
        
        Returns:
            页面标题
        """
        pass
    
    @abstractmethod
    def get_page_key(self) -> str:
        """
        获取页面键（子类必须实现）
        
        Returns:
            页面唯一标识
        """
        pass
    
    def load_page(self) -> None:
        """加载页面数据"""
        if self.is_loading or self.is_loaded:
            return
        
        self.is_loading = True
        
        try:
            self._load_page_data()
            self.is_loaded = True
            self.page_loaded.emit()
            self.logger.info(f"页面加载完成: {self.get_page_title()}")
            
        except Exception as e:
            self.logger.error(f"页面加载失败: {e}")
            self.page_error.emit(str(e))
            
        finally:
            self.is_loading = False
    
    def _load_page_data(self) -> None:
        """加载页面数据（子类可重写）"""
        pass
    
    def refresh_page(self) -> None:
        """刷新页面"""
        self.is_loaded = False
        self.load_page()
        self.logger.info(f"页面刷新: {self.get_page_title()}")
    
    def cleanup_page(self) -> None:
        """清理页面资源（子类可重写）"""
        pass
    
    def show_status_message(self, message: str, timeout: int = 3000) -> None:
        """
        显示状态消息
        
        Args:
            message: 消息内容
            timeout: 超时时间（毫秒）
        """
        self.status_message.emit(message, timeout)
    
    def showEvent(self, event) -> None:
        """页面显示事件"""
        super().showEvent(event)
        
        # 延迟加载页面数据
        if not self.is_loaded and not self.is_loading:
            QTimer.singleShot(100, self.load_page)


class PlaceholderPage(BasePage):
    """
    占位页面
    
    用于暂未实现的页面功能。
    """
    
    def __init__(self, page_key: str, page_title: str, parent: Optional[QWidget] = None):
        """
        初始化占位页面
        
        Args:
            page_key: 页面键
            page_title: 页面标题
            parent: 父窗口
        """
        self._page_key = page_key
        self._page_title = page_title
        super().__init__(parent)
    
    def get_page_title(self) -> str:
        """获取页面标题"""
        return self._page_title
    
    def get_page_key(self) -> str:
        """获取页面键"""
        return self._page_key
    
    def _create_page_content(self, layout: QVBoxLayout) -> None:
        """创建页面内容"""
        # 占位内容
        placeholder_label = QLabel("此功能正在开发中...")
        placeholder_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        placeholder_label.setStyleSheet("""
            QLabel {
                color: #666666;
                font-size: 14px;
                padding: 40px;
                background-color: #f9f9f9;
                border: 2px dashed #cccccc;
                border-radius: 8px;
            }
        """)
        
        layout.addWidget(placeholder_label)
        layout.addStretch()
    
    def _load_page_data(self) -> None:
        """加载页面数据"""
        # 占位页面无需加载数据
        pass


# 将HomePage移动到单独的文件中
    
    def _load_page_data(self) -> None:
        """加载页面数据"""
        # 这里可以加载首页的统计数据等
        self.show_status_message("首页数据加载完成")


# 页面工厂类
class PageFactory:
    """页面工厂类"""
    
    @classmethod
    def create_page(cls, page_key: str, parent: Optional[QWidget] = None) -> Optional[BasePage]:
        """
        创建页面实例

        Args:
            page_key: 页面键
            parent: 父窗口

        Returns:
            页面实例，如果页面键不存在则返回None
        """
        # 根据页面键创建对应的页面
        if page_key == "home":
            return cls._create_home_page(parent)
        elif page_key == "task_list":
            return cls._create_task_list_page(parent)
        elif page_key == "task_monitor":
            return PlaceholderPage("task_monitor", "任务监控")
        elif page_key == "task_history":
            return PlaceholderPage("task_history", "执行历史")
        elif page_key == "script_list":
            return cls._create_script_list_page(parent)
        elif page_key == "script_editor":
            return PlaceholderPage("script_editor", "脚本编辑器")
        elif page_key == "settings":
            return cls._create_settings_page(parent)
        elif page_key == "theme_settings":
            return cls._create_theme_settings_page(parent)
        else:
            return None

    @classmethod
    def _create_home_page(cls, parent: Optional[QWidget] = None) -> Optional[BasePage]:
        """创建首页"""
        try:
            from .home_page import HomePage
            return HomePage(parent)
        except ImportError:
            # 如果导入失败，返回占位页面
            return PlaceholderPage("home", "首页")
        except Exception:
            return None

    @classmethod
    def _create_settings_page(cls, parent: Optional[QWidget] = None) -> Optional[BasePage]:
        """创建系统设置页面"""
        try:
            from .settings_page import SettingsPage
            return SettingsPage(parent)
        except ImportError:
            # 如果导入失败，返回占位页面
            return PlaceholderPage("settings", "系统设置")
        except Exception:
            return None

    @classmethod
    def _create_theme_settings_page(cls, parent: Optional[QWidget] = None) -> Optional[BasePage]:
        """创建主题设置页面"""
        try:
            from .theme_settings_page import ThemeSettingsPage
            return ThemeSettingsPage(parent)
        except ImportError:
            # 如果导入失败，返回占位页面
            return PlaceholderPage("theme_settings", "主题设置")
        except Exception:
            return None

    @classmethod
    def _create_script_list_page(cls, parent: Optional[QWidget] = None) -> Optional[BasePage]:
        """创建脚本列表页面"""
        try:
            from .script_list_page import ScriptListPage
            return ScriptListPage(parent)
        except ImportError:
            # 如果导入失败，返回占位页面
            return PlaceholderPage("script_list", "脚本列表")
        except Exception:
            return None

    @classmethod
    def _create_task_list_page(cls, parent: Optional[QWidget] = None) -> Optional[BasePage]:
        """创建任务列表页面"""
        try:
            from .task_list_page import TaskListPage
            return TaskListPage(parent)
        except ImportError:
            # 如果导入失败，返回占位页面
            return PlaceholderPage("task_list", "任务列表")
        except Exception:
            return None
    
    @classmethod
    def register_page(cls, page_key: str, page_class) -> None:
        """
        注册页面类
        
        Args:
            page_key: 页面键
            page_class: 页面类
        """
        cls._page_classes[page_key] = page_class
