"""
系统配置数据模型

定义系统配置相关的数据模型类。
"""

from typing import Optional, Any, Dict
from sqlalchemy import String, Text, Enum, Boolean, JSON, Index, Integer
from sqlalchemy.orm import Mapped, mapped_column
import enum

from .base import BaseModel, AuditMixin


class ConfigType(enum.Enum):
    """配置类型枚举"""
    SYSTEM = "SYSTEM"        # 系统配置
    USER = "USER"           # 用户配置
    EXECUTION = "EXECUTION"  # 执行配置
    SECURITY = "SECURITY"    # 安全配置
    UI = "UI"               # 界面配置
    NOTIFICATION = "NOTIFICATION"  # 通知配置


class ConfigScope(enum.Enum):
    """配置作用域枚举"""
    GLOBAL = "GLOBAL"       # 全局配置
    USER = "USER"          # 用户级配置
    SESSION = "SESSION"     # 会话级配置


class Config(BaseModel, AuditMixin):
    """
    系统配置模型
    
    存储系统的各种配置信息。
    """
    __tablename__ = "configs"
    
    # 配置标识
    key: Mapped[str] = mapped_column(
        String(200),
        nullable=False,
        unique=True,
        comment="配置键"
    )
    
    name: Mapped[str] = mapped_column(
        String(200),
        nullable=False,
        comment="配置名称"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="配置描述"
    )
    
    # 配置分类
    config_type: Mapped[ConfigType] = mapped_column(
        Enum(ConfigType),
        nullable=False,
        comment="配置类型"
    )
    
    scope: Mapped[ConfigScope] = mapped_column(
        Enum(ConfigScope),
        default=ConfigScope.GLOBAL,
        nullable=False,
        comment="配置作用域"
    )
    
    category: Mapped[Optional[str]] = mapped_column(
        String(100),
        nullable=True,
        comment="配置分类"
    )
    
    # 配置值
    value: Mapped[Optional[Any]] = mapped_column(
        JSON,
        nullable=True,
        comment="配置值"
    )
    
    default_value: Mapped[Optional[Any]] = mapped_column(
        JSON,
        nullable=True,
        comment="默认值"
    )
    
    # 配置属性
    is_required: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        comment="是否必需"
    )
    
    is_readonly: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        comment="是否只读"
    )
    
    is_sensitive: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        comment="是否敏感信息"
    )
    
    # 验证规则
    validation_rules: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        comment="验证规则"
    )
    
    # 显示属性
    display_order: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        comment="显示顺序"
    )
    
    ui_component: Mapped[Optional[str]] = mapped_column(
        String(50),
        nullable=True,
        comment="UI组件类型"
    )
    
    ui_options: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        comment="UI选项"
    )
    
    # 索引定义
    __table_args__ = (
        Index('idx_config_key', 'key'),
        Index('idx_config_type', 'config_type'),
        Index('idx_config_scope', 'scope'),
        Index('idx_config_category', 'category'),
        Index('idx_config_display_order', 'display_order'),
    )
    
    def __init__(self, **kwargs):
        """初始化配置实例"""
        super().__init__(**kwargs)
        if not self.validation_rules:
            self.validation_rules = {}
        if not self.ui_options:
            self.ui_options = {}
    
    def get_effective_value(self) -> Any:
        """获取有效值（如果value为None则返回默认值）"""
        return self.value if self.value is not None else self.default_value
    
    def set_value(self, value: Any) -> bool:
        """
        设置配置值
        
        Args:
            value: 要设置的值
            
        Returns:
            是否设置成功
        """
        if self.is_readonly:
            return False
        
        # 验证值
        if not self.validate_value(value):
            return False
        
        self.value = value
        return True
    
    def validate_value(self, value: Any) -> bool:
        """
        验证配置值
        
        Args:
            value: 要验证的值
            
        Returns:
            是否有效
        """
        if not self.validation_rules:
            return True
        
        # 必需值检查
        if self.is_required and value is None:
            return False
        
        # 类型检查
        if 'type' in self.validation_rules:
            expected_type = self.validation_rules['type']
            if expected_type == 'string' and not isinstance(value, str):
                return False
            elif expected_type == 'integer' and not isinstance(value, int):
                return False
            elif expected_type == 'boolean' and not isinstance(value, bool):
                return False
            elif expected_type == 'number' and not isinstance(value, (int, float)):
                return False
        
        # 范围检查
        if 'min' in self.validation_rules and value < self.validation_rules['min']:
            return False
        if 'max' in self.validation_rules and value > self.validation_rules['max']:
            return False
        
        # 长度检查
        if 'min_length' in self.validation_rules and len(str(value)) < self.validation_rules['min_length']:
            return False
        if 'max_length' in self.validation_rules and len(str(value)) > self.validation_rules['max_length']:
            return False
        
        # 选项检查
        if 'options' in self.validation_rules and value not in self.validation_rules['options']:
            return False
        
        return True
    
    def reset_to_default(self) -> bool:
        """
        重置为默认值
        
        Returns:
            是否重置成功
        """
        if self.is_readonly:
            return False
        
        self.value = self.default_value
        return True
    
    def get_masked_value(self) -> Any:
        """获取掩码值（敏感信息显示为***）"""
        if self.is_sensitive and self.value:
            if isinstance(self.value, str):
                return '*' * len(self.value)
            else:
                return '***'
        return self.value
    
    @classmethod
    def get_by_key(cls, session, key: str) -> Optional['Config']:
        """根据键获取配置"""
        return session.query(cls).filter(cls.key == key, cls.is_deleted == False).first()
    
    @classmethod
    def get_by_type(cls, session, config_type: ConfigType) -> list:
        """根据类型获取配置列表"""
        return session.query(cls).filter(
            cls.config_type == config_type,
            cls.is_deleted == False
        ).order_by(cls.display_order, cls.name).all()
    
    @classmethod
    def get_by_category(cls, session, category: str) -> list:
        """根据分类获取配置列表"""
        return session.query(cls).filter(
            cls.category == category,
            cls.is_deleted == False
        ).order_by(cls.display_order, cls.name).all()
    
    def __repr__(self) -> str:
        """字符串表示"""
        return f"<Config(id={self.id}, key='{self.key}', type={self.config_type.value})>"
    
    def __str__(self) -> str:
        """用户友好的字符串表示"""
        return f"Config: {self.name} ({self.key})"
