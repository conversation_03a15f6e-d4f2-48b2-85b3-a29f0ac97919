"""
DAO层异常类

定义数据访问层的自定义异常。
"""

from typing import Optional, Any


class DAOException(Exception):
    """DAO层基础异常类"""
    
    def __init__(self, message: str, details: Optional[Any] = None):
        """
        初始化DAO异常
        
        Args:
            message: 异常消息
            details: 异常详细信息
        """
        super().__init__(message)
        self.message = message
        self.details = details
    
    def __str__(self) -> str:
        """字符串表示"""
        if self.details:
            return f"{self.message} - Details: {self.details}"
        return self.message


class EntityNotFoundError(DAOException):
    """实体未找到异常"""
    
    def __init__(self, entity_type: str, identifier: Any):
        """
        初始化实体未找到异常
        
        Args:
            entity_type: 实体类型
            identifier: 实体标识符
        """
        message = f"{entity_type} with identifier '{identifier}' not found"
        super().__init__(message, {"entity_type": entity_type, "identifier": identifier})
        self.entity_type = entity_type
        self.identifier = identifier


class DuplicateEntityError(DAOException):
    """重复实体异常"""
    
    def __init__(self, entity_type: str, field: str, value: Any):
        """
        初始化重复实体异常
        
        Args:
            entity_type: 实体类型
            field: 重复字段
            value: 重复值
        """
        message = f"{entity_type} with {field}='{value}' already exists"
        super().__init__(message, {"entity_type": entity_type, "field": field, "value": value})
        self.entity_type = entity_type
        self.field = field
        self.value = value


class ValidationError(DAOException):
    """数据验证异常"""
    
    def __init__(self, field: str, value: Any, reason: str):
        """
        初始化数据验证异常
        
        Args:
            field: 验证失败的字段
            value: 字段值
            reason: 失败原因
        """
        message = f"Validation failed for field '{field}': {reason}"
        super().__init__(message, {"field": field, "value": value, "reason": reason})
        self.field = field
        self.value = value
        self.reason = reason


class DatabaseConnectionError(DAOException):
    """数据库连接异常"""
    
    def __init__(self, operation: str, original_error: Optional[Exception] = None):
        """
        初始化数据库连接异常
        
        Args:
            operation: 执行的操作
            original_error: 原始异常
        """
        message = f"Database connection failed during operation: {operation}"
        if original_error:
            message += f" - {str(original_error)}"
        super().__init__(message, {"operation": operation, "original_error": original_error})
        self.operation = operation
        self.original_error = original_error


class TransactionError(DAOException):
    """事务处理异常"""
    
    def __init__(self, operation: str, original_error: Optional[Exception] = None):
        """
        初始化事务处理异常
        
        Args:
            operation: 执行的操作
            original_error: 原始异常
        """
        message = f"Transaction failed during operation: {operation}"
        if original_error:
            message += f" - {str(original_error)}"
        super().__init__(message, {"operation": operation, "original_error": original_error})
        self.operation = operation
        self.original_error = original_error


class QueryError(DAOException):
    """查询异常"""
    
    def __init__(self, query_type: str, parameters: Optional[dict] = None, 
                 original_error: Optional[Exception] = None):
        """
        初始化查询异常
        
        Args:
            query_type: 查询类型
            parameters: 查询参数
            original_error: 原始异常
        """
        message = f"Query failed: {query_type}"
        if original_error:
            message += f" - {str(original_error)}"
        super().__init__(message, {
            "query_type": query_type, 
            "parameters": parameters, 
            "original_error": original_error
        })
        self.query_type = query_type
        self.parameters = parameters
        self.original_error = original_error


class ConcurrencyError(DAOException):
    """并发控制异常"""
    
    def __init__(self, entity_type: str, identifier: Any, operation: str):
        """
        初始化并发控制异常
        
        Args:
            entity_type: 实体类型
            identifier: 实体标识符
            operation: 操作类型
        """
        message = f"Concurrency conflict for {entity_type} '{identifier}' during {operation}"
        super().__init__(message, {
            "entity_type": entity_type, 
            "identifier": identifier, 
            "operation": operation
        })
        self.entity_type = entity_type
        self.identifier = identifier
        self.operation = operation


class PermissionError(DAOException):
    """权限异常"""
    
    def __init__(self, operation: str, resource: str, user: Optional[str] = None):
        """
        初始化权限异常
        
        Args:
            operation: 操作类型
            resource: 资源
            user: 用户
        """
        message = f"Permission denied for operation '{operation}' on resource '{resource}'"
        if user:
            message += f" for user '{user}'"
        super().__init__(message, {"operation": operation, "resource": resource, "user": user})
        self.operation = operation
        self.resource = resource
        self.user = user


class DataIntegrityError(DAOException):
    """数据完整性异常"""
    
    def __init__(self, constraint: str, details: Optional[str] = None):
        """
        初始化数据完整性异常
        
        Args:
            constraint: 约束名称
            details: 详细信息
        """
        message = f"Data integrity constraint violation: {constraint}"
        if details:
            message += f" - {details}"
        super().__init__(message, {"constraint": constraint, "details": details})
        self.constraint = constraint
