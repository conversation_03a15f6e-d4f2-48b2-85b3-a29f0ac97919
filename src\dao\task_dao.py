"""
任务数据访问对象

提供任务相关的数据访问操作。
"""

from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from sqlalchemy import and_, or_, func
from sqlalchemy.orm import Session, joinedload

from ..models.task import Task, TaskStatus, TaskPriority, ScheduleType
from ..models.script import Script
from ..models.execution import Execution
from ..utils.logger import get_logger
from .base_dao import BaseDAO
from .exceptions import ValidationError, EntityNotFoundError

logger = get_logger(__name__)


class TaskDAO(BaseDAO[Task]):
    """任务数据访问对象"""
    
    def __init__(self):
        """初始化TaskDAO"""
        super().__init__(Task)
    
    def get_by_name(self, name: str) -> Optional[Task]:
        """
        根据名称获取任务
        
        Args:
            name: 任务名称
            
        Returns:
            任务实例或None
        """
        try:
            with self.get_session() as session:
                return session.query(Task).filter(
                    and_(
                        Task.name == name,
                        Task.is_deleted == False
                    )
                ).first()
        except Exception as e:
            logger.error(f"Error getting task by name {name}: {e}")
            raise
    
    def get_by_status(self, status: TaskStatus, 
                     include_deleted: bool = False) -> List[Task]:
        """
        根据状态获取任务列表
        
        Args:
            status: 任务状态
            include_deleted: 是否包含已删除的任务
            
        Returns:
            任务列表
        """
        try:
            with self.get_session() as session:
                query = session.query(Task).filter(Task.status == status)
                
                if not include_deleted:
                    query = query.filter(Task.is_deleted == False)
                
                return query.order_by(Task.priority.desc(), Task.created_at).all()
        except Exception as e:
            logger.error(f"Error getting tasks by status {status}: {e}")
            raise
    
    def get_by_priority(self, priority: TaskPriority, 
                       include_deleted: bool = False) -> List[Task]:
        """
        根据优先级获取任务列表
        
        Args:
            priority: 任务优先级
            include_deleted: 是否包含已删除的任务
            
        Returns:
            任务列表
        """
        try:
            with self.get_session() as session:
                query = session.query(Task).filter(Task.priority == priority)
                
                if not include_deleted:
                    query = query.filter(Task.is_deleted == False)
                
                return query.order_by(Task.created_at.desc()).all()
        except Exception as e:
            logger.error(f"Error getting tasks by priority {priority}: {e}")
            raise
    
    def get_by_script_id(self, script_id: int, 
                        include_deleted: bool = False) -> List[Task]:
        """
        根据脚本ID获取任务列表
        
        Args:
            script_id: 脚本ID
            include_deleted: 是否包含已删除的任务
            
        Returns:
            任务列表
        """
        try:
            with self.get_session() as session:
                query = session.query(Task).filter(Task.script_id == script_id)
                
                if not include_deleted:
                    query = query.filter(Task.is_deleted == False)
                
                return query.order_by(Task.created_at.desc()).all()
        except Exception as e:
            logger.error(f"Error getting tasks by script_id {script_id}: {e}")
            raise
    
    def get_schedulable_tasks(self, limit: Optional[int] = None) -> List[Task]:
        """
        获取可调度的任务列表
        
        Args:
            limit: 限制返回数量
            
        Returns:
            可调度任务列表
        """
        try:
            with self.get_session() as session:
                now = datetime.now()
                
                query = session.query(Task).filter(
                    and_(
                        Task.status.in_([TaskStatus.PENDING, TaskStatus.PAUSED]),
                        Task.is_deleted == False,
                        Task.schedule_type != ScheduleType.MANUAL,
                        or_(
                            Task.next_run_time.is_(None),
                            Task.next_run_time <= now
                        )
                    )
                ).order_by(Task.priority.desc(), Task.next_run_time)
                
                if limit:
                    query = query.limit(limit)
                
                return query.all()
        except Exception as e:
            logger.error(f"Error getting schedulable tasks: {e}")
            raise
    
    def get_pending_tasks(self, limit: Optional[int] = None) -> List[Task]:
        """
        获取待执行的任务列表
        
        Args:
            limit: 限制返回数量
            
        Returns:
            待执行任务列表
        """
        return self.get_by_status(TaskStatus.PENDING)[:limit] if limit else self.get_by_status(TaskStatus.PENDING)
    
    def get_running_tasks(self) -> List[Task]:
        """
        获取正在运行的任务列表
        
        Returns:
            正在运行的任务列表
        """
        return self.get_by_status(TaskStatus.RUNNING)
    
    def get_failed_tasks(self, include_retryable: bool = True) -> List[Task]:
        """
        获取失败的任务列表
        
        Args:
            include_retryable: 是否只包含可重试的任务
            
        Returns:
            失败任务列表
        """
        try:
            with self.get_session() as session:
                query = session.query(Task).filter(
                    and_(
                        Task.status == TaskStatus.FAILED,
                        Task.is_deleted == False
                    )
                )
                
                if include_retryable:
                    # 只返回可重试的任务
                    tasks = query.all()
                    return [task for task in tasks if task.can_retry()]
                else:
                    return query.order_by(Task.updated_at.desc()).all()
        except Exception as e:
            logger.error(f"Error getting failed tasks: {e}")
            raise
    
    def get_tasks_by_schedule_type(self, schedule_type: ScheduleType, 
                                  include_deleted: bool = False) -> List[Task]:
        """
        根据调度类型获取任务列表
        
        Args:
            schedule_type: 调度类型
            include_deleted: 是否包含已删除的任务
            
        Returns:
            任务列表
        """
        try:
            with self.get_session() as session:
                query = session.query(Task).filter(Task.schedule_type == schedule_type)
                
                if not include_deleted:
                    query = query.filter(Task.is_deleted == False)
                
                return query.order_by(Task.next_run_time).all()
        except Exception as e:
            logger.error(f"Error getting tasks by schedule_type {schedule_type}: {e}")
            raise
    
    def search_tasks(self, keyword: str, 
                    include_deleted: bool = False) -> List[Task]:
        """
        搜索任务
        
        Args:
            keyword: 搜索关键词
            include_deleted: 是否包含已删除的任务
            
        Returns:
            任务列表
        """
        try:
            with self.get_session() as session:
                search_pattern = f"%{keyword}%"
                query = session.query(Task).join(Script).filter(
                    or_(
                        Task.name.ilike(search_pattern),
                        Task.description.ilike(search_pattern),
                        Task.category.ilike(search_pattern),
                        Script.name.ilike(search_pattern)
                    )
                )
                
                if not include_deleted:
                    query = query.filter(Task.is_deleted == False)
                
                return query.order_by(Task.name).all()
        except Exception as e:
            logger.error(f"Error searching tasks by keyword {keyword}: {e}")
            raise
    
    def get_tasks_with_script(self, include_deleted: bool = False) -> List[Task]:
        """
        获取包含脚本信息的任务列表
        
        Args:
            include_deleted: 是否包含已删除的任务
            
        Returns:
            任务列表（预加载脚本信息）
        """
        try:
            with self.get_session() as session:
                query = session.query(Task).options(joinedload(Task.script))
                
                if not include_deleted:
                    query = query.filter(Task.is_deleted == False)
                
                return query.order_by(Task.created_at.desc()).all()
        except Exception as e:
            logger.error(f"Error getting tasks with script: {e}")
            raise
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取任务统计信息
        
        Returns:
            统计信息字典
        """
        try:
            with self.get_session() as session:
                # 总数统计
                total_count = session.query(Task).filter(Task.is_deleted == False).count()
                
                # 按状态统计
                status_stats = {}
                for status in TaskStatus:
                    count = session.query(Task).filter(
                        and_(
                            Task.status == status,
                            Task.is_deleted == False
                        )
                    ).count()
                    status_stats[status.value] = count
                
                # 按优先级统计
                priority_stats = {}
                for priority in TaskPriority:
                    count = session.query(Task).filter(
                        and_(
                            Task.priority == priority,
                            Task.is_deleted == False
                        )
                    ).count()
                    priority_stats[priority.value] = count
                
                # 按调度类型统计
                schedule_stats = {}
                for schedule_type in ScheduleType:
                    count = session.query(Task).filter(
                        and_(
                            Task.schedule_type == schedule_type,
                            Task.is_deleted == False
                        )
                    ).count()
                    schedule_stats[schedule_type.value] = count
                
                # 执行统计
                total_executions = session.query(func.sum(Task.execution_count)).filter(
                    Task.is_deleted == False
                ).scalar() or 0
                
                total_successes = session.query(func.sum(Task.success_count)).filter(
                    Task.is_deleted == False
                ).scalar() or 0
                
                # 计算平均成功率
                avg_success_rate = 0.0
                if total_executions > 0:
                    avg_success_rate = (total_successes / total_executions) * 100
                
                return {
                    "total_count": total_count,
                    "status_stats": status_stats,
                    "priority_stats": priority_stats,
                    "schedule_stats": schedule_stats,
                    "total_executions": total_executions,
                    "total_successes": total_successes,
                    "avg_success_rate": round(avg_success_rate, 2)
                }
        except Exception as e:
            logger.error(f"Error getting task statistics: {e}")
            raise
    
    def update_status(self, task_id: int, status: TaskStatus) -> Task:
        """
        更新任务状态
        
        Args:
            task_id: 任务ID
            status: 新状态
            
        Returns:
            更新后的任务实例
        """
        return self.update(task_id, status=status)
    
    def update_next_run_time(self, task_id: int, next_run_time: Optional[datetime]) -> Task:
        """
        更新下次执行时间
        
        Args:
            task_id: 任务ID
            next_run_time: 下次执行时间
            
        Returns:
            更新后的任务实例
        """
        return self.update(task_id, next_run_time=next_run_time)
    
    def increment_execution_count(self, task_id: int, success: bool = True) -> None:
        """
        增加任务执行计数
        
        Args:
            task_id: 任务ID
            success: 是否执行成功
        """
        try:
            with self.get_session() as session:
                task = session.query(Task).filter(Task.id == task_id).first()
                if task:
                    task.increment_execution_count(success)
                    session.flush()
                    logger.debug(f"Incremented execution count for task {task_id}")
                else:
                    raise EntityNotFoundError("Task", task_id)
        except EntityNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Error incrementing execution count for task {task_id}: {e}")
            raise
    
    def pause_task(self, task_id: int) -> Task:
        """
        暂停任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            更新后的任务实例
        """
        try:
            with self.get_session() as session:
                task = session.query(Task).filter(Task.id == task_id).first()
                if task:
                    task.pause()
                    session.flush()
                    session.refresh(task)
                    logger.debug(f"Paused task {task_id}")
                    return task
                else:
                    raise EntityNotFoundError("Task", task_id)
        except EntityNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Error pausing task {task_id}: {e}")
            raise
    
    def resume_task(self, task_id: int) -> Task:
        """
        恢复任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            更新后的任务实例
        """
        try:
            with self.get_session() as session:
                task = session.query(Task).filter(Task.id == task_id).first()
                if task:
                    task.resume()
                    session.flush()
                    session.refresh(task)
                    logger.debug(f"Resumed task {task_id}")
                    return task
                else:
                    raise EntityNotFoundError("Task", task_id)
        except EntityNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Error resuming task {task_id}: {e}")
            raise
    
    def cancel_task(self, task_id: int) -> Task:
        """
        取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            更新后的任务实例
        """
        try:
            with self.get_session() as session:
                task = session.query(Task).filter(Task.id == task_id).first()
                if task:
                    task.cancel()
                    session.flush()
                    session.refresh(task)
                    logger.debug(f"Cancelled task {task_id}")
                    return task
                else:
                    raise EntityNotFoundError("Task", task_id)
        except EntityNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Error cancelling task {task_id}: {e}")
            raise
    
    def _validate_create_data(self, data: Dict[str, Any]) -> None:
        """
        验证任务创建数据
        
        Args:
            data: 创建数据
            
        Raises:
            ValidationError: 验证失败
        """
        # 验证必需字段
        if not data.get('name'):
            raise ValidationError('name', data.get('name'), 'Name is required')
        
        if not data.get('script_id'):
            raise ValidationError('script_id', data.get('script_id'), 'Script ID is required')
        
        # 验证名称唯一性
        if self.get_by_name(data['name']):
            raise ValidationError('name', data['name'], 'Task name already exists')
        
        # 验证脚本存在性
        from .script_dao import ScriptDAO
        script_dao = ScriptDAO()
        if not script_dao.exists(data['script_id']):
            raise ValidationError('script_id', data['script_id'], 'Script does not exist')
    
    def _validate_update_data(self, instance: Task, data: Dict[str, Any]) -> None:
        """
        验证任务更新数据
        
        Args:
            instance: 现有任务实例
            data: 更新数据
            
        Raises:
            ValidationError: 验证失败
        """
        # 验证名称唯一性（如果更新名称）
        if 'name' in data and data['name'] != instance.name:
            existing = self.get_by_name(data['name'])
            if existing and existing.id != instance.id:
                raise ValidationError('name', data['name'], 'Task name already exists')
        
        # 验证脚本存在性（如果更新脚本ID）
        if 'script_id' in data and data['script_id'] != instance.script_id:
            from .script_dao import ScriptDAO
            script_dao = ScriptDAO()
            if not script_dao.exists(data['script_id']):
                raise ValidationError('script_id', data['script_id'], 'Script does not exist')
