# 主题配置文件

本目录包含应用程序的主题配置文件。

## 目录结构

- `system/` - 系统内置主题
- `custom/` - 用户自定义主题

## 主题文件格式

主题文件采用JSON格式，包含以下结构：

```json
{
  "metadata": {
    "name": "主题名称",
    "version": "1.0.0",
    "author": "作者",
    "description": "主题描述",
    "created_at": "2025-07-24T00:00:00",
    "updated_at": "2025-07-24T00:00:00",
    "tags": ["标签1", "标签2"],
    "is_system": false,
    "is_custom": true
  },
  "colors": {
    "primary": "#1976d2",
    "secondary": "#424242",
    "success": "#4caf50",
    "warning": "#ff9800",
    "danger": "#f44336",
    "info": "#2196f3",
    "background": "#ffffff",
    "surface": "#f5f5f5",
    "text_primary": "#212121",
    "text_secondary": "#757575",
    "border": "#e0e0e0",
    "shadow": "#00000020"
  },
  "fonts": {
    "family": "Segoe UI",
    "sizes": {
      "small": 12,
      "medium": 14,
      "large": 16,
      "xlarge": 18,
      "xxlarge": 20
    }
  },
  "spacing": {
    "tiny": 4,
    "small": 8,
    "medium": 16,
    "large": 24,
    "xlarge": 32,
    "xxlarge": 48
  },
  "border_radius": {
    "small": 4,
    "medium": 8,
    "large": 12
  },
  "shadows": {
    "small": "0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)",
    "medium": "0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23)",
    "large": "0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23)"
  },
  "animations": {
    "duration_fast": 150,
    "duration_normal": 300,
    "duration_slow": 500,
    "easing": "ease-in-out"
  }
}
```

## 使用说明

1. 系统主题由应用程序自动创建和管理
2. 用户可以通过主题编辑器创建自定义主题
3. 主题文件可以导入和导出
4. 主题配置支持热重载
