# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
.venv/
venv/
ENV/
env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Application specific
data/*.db
logs/*.log
temp/*
scripts/*.py
scripts/*.sh
scripts/*.bat


# Test coverage
htmlcov/
.coverage
.coverage.*
.pytest_cache/
coverage.xml
*.cover
*.py,cover
.hypothesis/
nosetests.xml
acceptance_tests/
tests/
logs/
data/

# MyPy
.mypy_cache/
.dmypy.json
dmypy.json

# PyQt specific
*.qrc
*.ui~

# Build artifacts
*.exe
*.msi
*.spec

# Configuration files with sensitive data
.env
.env.local
.env.production
config/local.py
config/production.py

# Documentation build
docs/_build/
docs/build/

# Acceptance test reports and logs
acceptance_tests/reports/
acceptance_tests/logs/
acceptance_tests/coverage/

# Temporary files
temp
