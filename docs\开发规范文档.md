# 自动化任务管理工具开发规范文档

## 📋 文档概述

### 文档目的
本文档制定了自动化任务管理工具项目的开发规范，包括编码标准、项目结构、测试要求、版本控制、部署流程等，确保代码质量和团队协作效率。

### 适用范围
- 项目开发团队所有成员
- 代码审查和质量控制
- 项目维护和扩展
- 新成员培训和指导

### 规范原则
- **一致性**: 统一的编码风格和项目结构
- **可读性**: 清晰的代码逻辑和充分的注释
- **可维护性**: 模块化设计和良好的文档
- **可测试性**: 完善的测试覆盖和自动化测试
- **安全性**: 安全的编码实践和数据保护

## 📑 目录

1. [项目结构规范](#项目结构规范)
2. [编码规范](#编码规范)
3. [注释和文档规范](#注释和文档规范)
4. [测试规范](#测试规范)
5. [版本控制规范](#版本控制规范)
6. [部署规范](#部署规范)
7. [代码审查规范](#代码审查规范)
8. [性能优化规范](#性能优化规范)

## 📁 项目结构规范

### 1.1 目录结构

#### 标准项目结构
```
task_manager/
├── src/                          # 源代码目录
│   ├── __init__.py
│   ├── main.py                   # 应用程序入口
│   ├── config/                   # 配置模块
│   │   ├── __init__.py
│   │   ├── settings.py           # 应用配置
│   │   └── database.py           # 数据库配置
│   ├── core/                     # 核心模块
│   │   ├── __init__.py
│   │   ├── event_bus.py          # 事件总线
│   │   ├── logger.py             # 日志系统
│   │   ├── security.py           # 安全模块
│   │   └── utils.py              # 工具函数
│   ├── models/                   # 数据模型
│   │   ├── __init__.py
│   │   ├── base.py               # 基础模型
│   │   ├── task.py               # 任务模型
│   │   ├── script.py             # 脚本模型
│   │   └── execution.py          # 执行记录模型
│   ├── dao/                      # 数据访问层
│   │   ├── __init__.py
│   │   ├── base_dao.py           # 基础DAO
│   │   ├── task_dao.py           # 任务DAO
│   │   ├── script_dao.py         # 脚本DAO
│   │   └── execution_dao.py      # 执行记录DAO
│   ├── services/                 # 业务服务层
│   │   ├── __init__.py
│   │   ├── task_service.py       # 任务服务
│   │   ├── script_service.py     # 脚本服务
│   │   ├── execution_service.py  # 执行服务
│   │   └── scheduler_service.py  # 调度服务
│   ├── ui/                       # 用户界面
│   │   ├── __init__.py
│   │   ├── main_window.py        # 主窗口
│   │   ├── widgets/              # UI组件
│   │   │   ├── __init__.py
│   │   │   ├── task_widget.py    # 任务管理组件
│   │   │   ├── script_widget.py  # 脚本管理组件
│   │   │   └── settings_widget.py # 设置组件
│   │   ├── dialogs/              # 对话框
│   │   │   ├── __init__.py
│   │   │   ├── task_dialog.py    # 任务编辑对话框
│   │   │   └── settings_dialog.py # 设置对话框
│   │   └── resources/            # 资源文件
│   │       ├── icons/            # 图标文件
│   │       ├── styles/           # 样式文件
│   │       └── translations/     # 翻译文件
│   └── workers/                  # 后台工作线程
│       ├── __init__.py
│       ├── task_worker.py        # 任务执行工作线程
│       └── monitor_worker.py     # 监控工作线程
├── tests/                        # 测试代码
│   ├── __init__.py
│   ├── conftest.py               # pytest配置
│   ├── unit/                     # 单元测试
│   │   ├── test_models.py
│   │   ├── test_services.py
│   │   └── test_dao.py
│   ├── integration/              # 集成测试
│   │   ├── test_task_flow.py
│   │   └── test_script_execution.py
│   ├── ui/                       # UI测试
│   │   ├── test_main_window.py
│   │   └── test_widgets.py
│   └── fixtures/                 # 测试数据
│       ├── sample_scripts/
│       └── test_data.json
├── docs/                         # 文档目录
│   ├── api/                      # API文档
│   ├── user_guide/               # 用户指南
│   └── developer_guide/          # 开发指南
├── scripts/                      # 构建和部署脚本
│   ├── build.py                  # 构建脚本
│   ├── deploy.py                 # 部署脚本
│   └── setup_dev.py              # 开发环境设置
├── requirements/                 # 依赖文件
│   ├── base.txt                  # 基础依赖
│   ├── dev.txt                   # 开发依赖
│   └── test.txt                  # 测试依赖
├── .gitignore                    # Git忽略文件
├── .flake8                       # Flake8配置
├── .pre-commit-config.yaml       # Pre-commit配置
├── pyproject.toml                # 项目配置
├── README.md                     # 项目说明
└── CHANGELOG.md                  # 变更日志
```

### 1.2 文件命名规范

#### 命名约定
```python
# 模块文件命名：小写字母+下划线
task_service.py
script_manager.py
database_config.py

# 类文件命名：与主要类名对应
class TaskService:  # 对应文件 task_service.py
class ScriptManager:  # 对应文件 script_manager.py

# 测试文件命名：test_ + 被测试模块名
test_task_service.py
test_script_manager.py

# 配置文件命名：功能描述 + 类型
database_config.py
logging_config.py
app_settings.py
```

#### 包和模块组织
```python
# 包的__init__.py文件应该包含：
# 1. 包的简要说明
# 2. 主要类和函数的导入
# 3. 版本信息（如果适用）

"""
Task management services package.

This package contains all business logic services for task management,
including task creation, execution, scheduling, and monitoring.
"""

from .task_service import TaskService
from .script_service import ScriptService
from .execution_service import ExecutionService
from .scheduler_service import SchedulerService

__all__ = [
    'TaskService',
    'ScriptService', 
    'ExecutionService',
    'SchedulerService'
]

__version__ = '1.0.0'
```

## 💻 编码规范

### 2.1 Python编码标准

#### PEP 8 遵循
```python
# 导入顺序：标准库 -> 第三方库 -> 本地模块
import os
import sys
from datetime import datetime
from typing import List, Dict, Optional

from PyQt6.QtWidgets import QWidget, QVBoxLayout
from PyQt6.QtCore import QObject, pyqtSignal
from sqlalchemy.orm import Session

from src.models.task import Task
from src.dao.task_dao import TaskDAO
from src.core.logger import get_logger

# 行长度限制：88字符（Black默认）
def create_task_with_very_long_parameter_names(
    task_name: str,
    task_description: str,
    script_content: str,
    execution_parameters: Dict[str, Any]
) -> Task:
    """创建任务的函数示例，展示长参数列表的格式化。"""
    pass

# 类定义
class TaskService(QObject):
    """任务管理服务类。
    
    负责任务的创建、更新、删除和查询操作，
    以及任务执行状态的管理。
    """
    
    # 类变量
    DEFAULT_TIMEOUT = 3600
    MAX_RETRY_COUNT = 3
    
    # 信号定义
    task_created = pyqtSignal(dict)
    task_updated = pyqtSignal(dict)
    
    def __init__(self, task_dao: TaskDAO):
        """初始化任务服务。
        
        Args:
            task_dao: 任务数据访问对象
        """
        super().__init__()
        self._task_dao = task_dao
        self._logger = get_logger(__name__)
    
    def create_task(self, task_data: Dict[str, Any]) -> Task:
        """创建新任务。
        
        Args:
            task_data: 任务数据字典
            
        Returns:
            创建的任务对象
            
        Raises:
            ValidationError: 当任务数据验证失败时
            DatabaseError: 当数据库操作失败时
        """
        # 验证输入数据
        self._validate_task_data(task_data)
        
        # 创建任务对象
        task = Task(**task_data)
        
        # 保存到数据库
        try:
            task_id = self._task_dao.create(task)
            task.id = task_id
            
            # 记录日志
            self._logger.info(f"Task created successfully: {task.name} (ID: {task_id})")
            
            # 发射信号
            self.task_created.emit(task.to_dict())
            
            return task
            
        except Exception as e:
            self._logger.error(f"Failed to create task: {e}")
            raise DatabaseError(f"Failed to create task: {e}") from e
    
    def _validate_task_data(self, task_data: Dict[str, Any]) -> None:
        """验证任务数据。
        
        Args:
            task_data: 要验证的任务数据
            
        Raises:
            ValidationError: 当数据验证失败时
        """
        required_fields = ['name', 'script_id', 'schedule_type']
        
        for field in required_fields:
            if field not in task_data or not task_data[field]:
                raise ValidationError(f"Missing required field: {field}")
        
        # 验证任务名称长度
        if len(task_data['name']) > 255:
            raise ValidationError("Task name too long (max 255 characters)")
        
        # 验证调度类型
        valid_schedule_types = ['ONCE', 'INTERVAL', 'CRON']
        if task_data['schedule_type'] not in valid_schedule_types:
            raise ValidationError(f"Invalid schedule type: {task_data['schedule_type']}")
```

#### 类型注解规范
```python
from typing import List, Dict, Optional, Union, Any, Callable, TypeVar, Generic
from dataclasses import dataclass
from enum import Enum

# 使用类型注解
def process_tasks(
    tasks: List[Task],
    filters: Optional[Dict[str, Any]] = None,
    callback: Optional[Callable[[Task], None]] = None
) -> List[Task]:
    """处理任务列表。"""
    pass

# 泛型类型
T = TypeVar('T')

class Repository(Generic[T]):
    """通用仓储类。"""
    
    def get_by_id(self, entity_id: int) -> Optional[T]:
        """根据ID获取实体。"""
        pass
    
    def get_all(self) -> List[T]:
        """获取所有实体。"""
        pass

# 枚举类型
class TaskStatus(Enum):
    """任务状态枚举。"""
    INACTIVE = "INACTIVE"
    ACTIVE = "ACTIVE"
    RUNNING = "RUNNING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    CANCELLED = "CANCELLED"

# 数据类
@dataclass
class TaskConfig:
    """任务配置数据类。"""
    name: str
    timeout: int = 3600
    retry_count: int = 3
    parameters: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.parameters is None:
            self.parameters = {}
```

### 2.2 错误处理规范

#### 异常定义和处理
```python
# 自定义异常类
class TaskManagerError(Exception):
    """任务管理器基础异常类。"""
    pass

class ValidationError(TaskManagerError):
    """数据验证异常。"""
    pass

class DatabaseError(TaskManagerError):
    """数据库操作异常。"""
    pass

class ExecutionError(TaskManagerError):
    """任务执行异常。"""
    pass

class ConfigurationError(TaskManagerError):
    """配置错误异常。"""
    pass

# 异常处理示例
def execute_task(self, task_id: int) -> int:
    """执行任务。"""
    try:
        # 获取任务
        task = self._task_dao.get_by_id(task_id)
        if not task:
            raise ValidationError(f"Task not found: {task_id}")
        
        # 检查任务状态
        if task.status != TaskStatus.ACTIVE.value:
            raise ValidationError(f"Task is not active: {task.status}")
        
        # 执行任务
        execution_id = self._execution_service.execute(task)
        
        self._logger.info(f"Task execution started: {task_id} -> {execution_id}")
        return execution_id
        
    except ValidationError:
        # 验证错误直接重新抛出
        raise
    except DatabaseError:
        # 数据库错误直接重新抛出
        raise
    except Exception as e:
        # 其他异常包装为执行错误
        self._logger.error(f"Unexpected error executing task {task_id}: {e}")
        raise ExecutionError(f"Failed to execute task: {e}") from e

# 上下文管理器用于资源管理
from contextlib import contextmanager

@contextmanager
def database_transaction(session: Session):
    """数据库事务上下文管理器。"""
    try:
        yield session
        session.commit()
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()

# 使用示例
def create_task_with_transaction(self, task_data: Dict[str, Any]) -> Task:
    """在事务中创建任务。"""
    with database_transaction(self._session) as session:
        task = Task(**task_data)
        session.add(task)
        session.flush()  # 获取ID但不提交
        
        # 创建相关记录
        self._create_task_schedule(session, task.id, task_data.get('schedule'))
        
        return task
```

### 2.3 日志记录规范

#### 日志配置和使用
```python
import logging
import logging.handlers
from pathlib import Path

# 日志配置
def setup_logging(log_level: str = "INFO", log_file: str = None):
    """设置日志配置。"""
    
    # 创建根日志器
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # 清除现有处理器
    logger.handlers.clear()
    
    # 创建格式器
    formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file:
        # 确保日志目录存在
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 使用轮转文件处理器
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)

# 日志使用示例
class TaskService:
    """任务服务类。"""
    
    def __init__(self):
        self._logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def create_task(self, task_data: Dict[str, Any]) -> Task:
        """创建任务。"""
        self._logger.info(f"Creating task: {task_data.get('name', 'Unknown')}")
        
        try:
            # 验证数据
            self._logger.debug(f"Validating task data: {task_data}")
            self._validate_task_data(task_data)
            
            # 创建任务
            task = Task(**task_data)
            task_id = self._task_dao.create(task)
            
            self._logger.info(f"Task created successfully: {task.name} (ID: {task_id})")
            return task
            
        except ValidationError as e:
            self._logger.warning(f"Task validation failed: {e}")
            raise
        except Exception as e:
            self._logger.error(f"Failed to create task: {e}", exc_info=True)
            raise

# 结构化日志记录
import json

def log_task_execution(logger: logging.Logger, task: Task, execution_id: int, status: str):
    """记录任务执行日志。"""
    log_data = {
        'event': 'task_execution',
        'task_id': task.id,
        'task_name': task.name,
        'execution_id': execution_id,
        'status': status,
        'timestamp': datetime.now().isoformat()
    }
    
    logger.info(f"Task execution event: {json.dumps(log_data)}")
```

## 📝 注释和文档规范

### 3.1 代码注释规范

#### 文档字符串规范
```python
def create_task(
    self,
    name: str,
    script_id: int,
    schedule_type: str,
    parameters: Optional[Dict[str, Any]] = None,
    timeout: int = 3600
) -> Task:
    """创建新的任务。

    此方法用于创建一个新的任务实例，包括验证输入数据、
    创建任务对象并保存到数据库。

    Args:
        name: 任务名称，必须唯一且不超过255个字符
        script_id: 关联的脚本ID，必须是有效的脚本
        schedule_type: 调度类型，可选值：'ONCE', 'INTERVAL', 'CRON'
        parameters: 任务执行参数，可选的键值对字典
        timeout: 任务超时时间，单位为秒，默认3600秒

    Returns:
        Task: 创建成功的任务对象，包含分配的ID

    Raises:
        ValidationError: 当输入数据验证失败时抛出
        DatabaseError: 当数据库操作失败时抛出
        ScriptNotFoundError: 当指定的脚本不存在时抛出

    Example:
        >>> task_service = TaskService()
        >>> task = task_service.create_task(
        ...     name="数据备份任务",
        ...     script_id=1,
        ...     schedule_type="CRON",
        ...     parameters={"backup_path": "/data/backup"},
        ...     timeout=7200
        ... )
        >>> print(f"Created task: {task.name} (ID: {task.id})")
        Created task: 数据备份任务 (ID: 123)

    Note:
        - 任务名称在系统中必须唯一
        - 创建成功后会自动发送task_created信号
        - 任务默认状态为INACTIVE，需要手动激活

    See Also:
        update_task: 更新现有任务
        delete_task: 删除任务
        get_task: 获取任务详情
    """
    pass

class TaskExecutionWorker(QRunnable):
    """任务执行工作线程。

    此类负责在后台线程中执行任务脚本，避免阻塞主UI线程。
    支持任务的启动、停止、进度报告和结果收集。

    Attributes:
        task (Task): 要执行的任务对象
        execution_id (int): 执行记录ID
        signals (TaskExecutionSignals): 信号对象，用于与主线程通信
        _stop_flag (bool): 停止标志，用于中断执行

    Example:
        >>> worker = TaskExecutionWorker(task, execution_id)
        >>> worker.signals.progress.connect(self.on_progress_updated)
        >>> worker.signals.completed.connect(self.on_task_completed)
        >>> thread_pool.start(worker)
    """

    def __init__(self, task: Task, execution_id: int):
        """初始化任务执行工作线程。

        Args:
            task: 要执行的任务对象
            execution_id: 执行记录ID
        """
        super().__init__()
        self.task = task
        self.execution_id = execution_id
        self.signals = TaskExecutionSignals()
        self._stop_flag = False
```

#### 行内注释规范
```python
def process_task_queue(self):
    """处理任务队列。"""
    while self._running:
        try:
            # 从队列中获取任务，超时时间1秒
            task = self._task_queue.get(timeout=1)

            # 检查任务是否已被取消
            if task.status == TaskStatus.CANCELLED.value:
                self._logger.info(f"Skipping cancelled task: {task.id}")
                continue

            # 检查系统资源是否充足
            if not self._check_system_resources():
                # 资源不足时将任务重新放回队列
                self._task_queue.put(task)
                time.sleep(5)  # 等待5秒后重试
                continue

            # 执行任务
            self._execute_task(task)

        except queue.Empty:
            # 队列为空，继续循环
            continue
        except Exception as e:
            # 记录未预期的错误
            self._logger.error(f"Unexpected error in task queue processing: {e}")
            time.sleep(1)  # 短暂延迟避免快速循环

def _check_system_resources(self) -> bool:
    """检查系统资源是否充足。

    Returns:
        bool: 如果系统资源充足返回True，否则返回False
    """
    # 获取CPU使用率
    cpu_percent = psutil.cpu_percent(interval=1)

    # 获取内存使用率
    memory = psutil.virtual_memory()
    memory_percent = memory.percent

    # 检查资源阈值
    # CPU使用率不超过80%，内存使用率不超过85%
    return cpu_percent < 80 and memory_percent < 85
```

#### TODO和FIXME注释
```python
def optimize_task_scheduling(self):
    """优化任务调度算法。"""
    # TODO: 实现基于优先级的任务调度
    # 当前使用简单的FIFO队列，需要改进为优先级队列
    # 预计完成时间：2024-01-15
    # 负责人：张三

    # FIXME: 修复并发执行时的资源竞争问题
    # 问题描述：多个任务同时访问同一资源时可能出现冲突
    # 临时解决方案：使用文件锁
    # 永久解决方案：实现资源池管理

    # HACK: 临时解决方案，等待更好的实现
    # 使用sleep来避免CPU占用过高，不是最优解决方案
    time.sleep(0.1)

    # NOTE: 重要说明
    # 此方法在系统启动时调用，确保调度器正确初始化
    pass
```

### 3.2 API文档规范

#### 模块级文档
```python
"""
Task Management Services Module

This module provides comprehensive task management functionality including:
- Task creation, modification, and deletion
- Task scheduling and execution
- Task monitoring and status tracking
- Task history and logging

The module follows a service-oriented architecture with clear separation
between data access, business logic, and presentation layers.

Classes:
    TaskService: Main service class for task management operations
    TaskExecutionService: Handles task execution and monitoring
    TaskSchedulerService: Manages task scheduling and timing

Functions:
    create_task_from_template: Creates a task from a predefined template
    validate_task_configuration: Validates task configuration data

Constants:
    DEFAULT_TASK_TIMEOUT: Default timeout for task execution (3600 seconds)
    MAX_CONCURRENT_TASKS: Maximum number of concurrent task executions (3)

Example:
    Basic usage of the task management services:

    >>> from src.services.task_service import TaskService
    >>> task_service = TaskService()
    >>> task = task_service.create_task(
    ...     name="Example Task",
    ...     script_id=1,
    ...     schedule_type="ONCE"
    ... )
    >>> print(f"Created task: {task.name}")
    Created task: Example Task

Author: Development Team
Version: 1.0.0
Last Modified: 2024-12-19
"""

# 模块级常量
DEFAULT_TASK_TIMEOUT = 3600
MAX_CONCURRENT_TASKS = 3
SUPPORTED_SCRIPT_TYPES = ['PYTHON', 'SHELL', 'BATCH', 'POWERSHELL']

# 模块级变量
_logger = logging.getLogger(__name__)
_task_service_instance = None
```

## 🧪 测试规范

### 4.1 测试结构和命名

#### 测试文件组织
```python
# tests/conftest.py - pytest配置文件
import pytest
from unittest.mock import Mock
from src.models.task import Task
from src.dao.task_dao import TaskDAO
from src.services.task_service import TaskService

@pytest.fixture
def mock_task_dao():
    """模拟任务DAO的fixture。"""
    return Mock(spec=TaskDAO)

@pytest.fixture
def sample_task():
    """示例任务对象的fixture。"""
    return Task(
        id=1,
        name="Test Task",
        script_id=1,
        schedule_type="ONCE",
        status="INACTIVE"
    )

@pytest.fixture
def task_service(mock_task_dao):
    """任务服务的fixture。"""
    return TaskService(task_dao=mock_task_dao)

# tests/unit/test_task_service.py - 单元测试
import pytest
from unittest.mock import Mock, patch
from src.services.task_service import TaskService
from src.models.task import Task
from src.exceptions import ValidationError, DatabaseError

class TestTaskService:
    """任务服务单元测试类。"""

    def test_create_task_success(self, task_service, mock_task_dao, sample_task):
        """测试成功创建任务。"""
        # Arrange
        task_data = {
            'name': 'Test Task',
            'script_id': 1,
            'schedule_type': 'ONCE'
        }
        mock_task_dao.create.return_value = 1

        # Act
        result = task_service.create_task(task_data)

        # Assert
        assert result.name == 'Test Task'
        assert result.id == 1
        mock_task_dao.create.assert_called_once()

    def test_create_task_validation_error(self, task_service):
        """测试创建任务时的验证错误。"""
        # Arrange
        invalid_task_data = {
            'name': '',  # 空名称应该引发验证错误
            'script_id': 1,
            'schedule_type': 'ONCE'
        }

        # Act & Assert
        with pytest.raises(ValidationError) as exc_info:
            task_service.create_task(invalid_task_data)

        assert "name" in str(exc_info.value)

    def test_create_task_database_error(self, task_service, mock_task_dao):
        """测试创建任务时的数据库错误。"""
        # Arrange
        task_data = {
            'name': 'Test Task',
            'script_id': 1,
            'schedule_type': 'ONCE'
        }
        mock_task_dao.create.side_effect = Exception("Database connection failed")

        # Act & Assert
        with pytest.raises(DatabaseError):
            task_service.create_task(task_data)

    @patch('src.services.task_service.datetime')
    def test_update_task_last_modified(self, mock_datetime, task_service, mock_task_dao, sample_task):
        """测试更新任务时自动设置最后修改时间。"""
        # Arrange
        mock_datetime.now.return_value = "2024-12-19 10:00:00"
        mock_task_dao.get_by_id.return_value = sample_task
        update_data = {'name': 'Updated Task Name'}

        # Act
        result = task_service.update_task(1, update_data)

        # Assert
        assert result.name == 'Updated Task Name'
        mock_task_dao.update.assert_called_once()
```

### 4.2 测试覆盖率要求

#### 覆盖率目标
```python
# pytest.ini 配置文件
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts =
    --cov=src
    --cov-report=html
    --cov-report=term-missing
    --cov-fail-under=80
    --strict-markers
    --disable-warnings

markers =
    unit: Unit tests
    integration: Integration tests
    ui: UI tests
    slow: Slow running tests
    smoke: Smoke tests

# 运行测试命令
# 运行所有测试
pytest

# 运行单元测试
pytest -m unit

# 运行集成测试
pytest -m integration

# 运行UI测试
pytest -m ui

# 生成覆盖率报告
pytest --cov=src --cov-report=html

# 运行特定测试文件
pytest tests/unit/test_task_service.py

# 运行特定测试方法
pytest tests/unit/test_task_service.py::TestTaskService::test_create_task_success
```

#### 集成测试示例
```python
# tests/integration/test_task_execution_flow.py
import pytest
import tempfile
import os
from src.services.task_service import TaskService
from src.services.script_service import ScriptService
from src.services.execution_service import ExecutionService
from src.dao.task_dao import TaskDAO
from src.dao.script_dao import ScriptDAO
from src.models.task import Task
from src.models.script import Script

@pytest.mark.integration
class TestTaskExecutionFlow:
    """任务执行流程集成测试。"""

    @pytest.fixture(autouse=True)
    def setup_services(self, db_session):
        """设置测试服务。"""
        self.task_dao = TaskDAO(db_session)
        self.script_dao = ScriptDAO(db_session)
        self.task_service = TaskService(self.task_dao)
        self.script_service = ScriptService(self.script_dao)
        self.execution_service = ExecutionService()

    def test_complete_task_execution_flow(self):
        """测试完整的任务执行流程。"""
        # 1. 创建脚本
        script_data = {
            'name': 'Test Script',
            'script_type': 'PYTHON',
            'content': 'print("Hello, World!")'
        }
        script = self.script_service.create_script(script_data)

        # 2. 创建任务
        task_data = {
            'name': 'Test Task',
            'script_id': script.id,
            'schedule_type': 'ONCE'
        }
        task = self.task_service.create_task(task_data)

        # 3. 执行任务
        execution_id = self.execution_service.execute_task(task)

        # 4. 等待执行完成
        execution_result = self.execution_service.wait_for_completion(execution_id, timeout=30)

        # 5. 验证结果
        assert execution_result['status'] == 'COMPLETED'
        assert 'Hello, World!' in execution_result['output']

        # 6. 验证任务状态更新
        updated_task = self.task_service.get_task(task.id)
        assert updated_task.last_run_time is not None
        assert updated_task.total_runs == 1
        assert updated_task.success_runs == 1
```

### 4.3 UI测试规范

#### PyQt6 UI测试
```python
# tests/ui/test_main_window.py
import pytest
from PyQt6.QtWidgets import QApplication
from PyQt6.QtTest import QTest
from PyQt6.QtCore import Qt
from src.ui.main_window import MainWindow
from src.services.task_service import TaskService

@pytest.mark.ui
class TestMainWindow:
    """主窗口UI测试类。"""

    @pytest.fixture(autouse=True)
    def setup_ui(self, qtbot):
        """设置UI测试环境。"""
        self.main_window = MainWindow()
        self.main_window.show()
        qtbot.addWidget(self.main_window)

        # 等待窗口完全加载
        qtbot.waitForWindowShown(self.main_window)

    def test_main_window_initialization(self, qtbot):
        """测试主窗口初始化。"""
        # 验证窗口标题
        assert self.main_window.windowTitle() == "自动化任务管理工具"

        # 验证窗口大小
        assert self.main_window.width() >= 1200
        assert self.main_window.height() >= 800

        # 验证菜单栏存在
        assert self.main_window.menuBar() is not None

        # 验证工具栏存在
        assert self.main_window.toolBar() is not None

        # 验证状态栏存在
        assert self.main_window.statusBar() is not None

    def test_menu_actions(self, qtbot):
        """测试菜单操作。"""
        # 测试文件菜单
        file_menu = self.main_window.menuBar().findChild(QMenu, "file_menu")
        assert file_menu is not None

        # 测试新建任务菜单项
        new_task_action = file_menu.findChild(QAction, "new_task_action")
        assert new_task_action is not None

        # 模拟点击新建任务
        with qtbot.waitSignal(new_task_action.triggered, timeout=1000):
            new_task_action.trigger()

    def test_task_creation_dialog(self, qtbot):
        """测试任务创建对话框。"""
        # 打开任务创建对话框
        self.main_window.show_create_task_dialog()

        # 获取对话框
        dialog = self.main_window.findChild(QDialog, "task_create_dialog")
        assert dialog is not None
        assert dialog.isVisible()

        # 填写任务信息
        name_input = dialog.findChild(QLineEdit, "task_name_input")
        name_input.setText("Test Task")

        # 点击确定按钮
        ok_button = dialog.findChild(QPushButton, "ok_button")
        qtbot.mouseClick(ok_button, Qt.MouseButton.LeftButton)

        # 验证对话框关闭
        qtbot.waitUntil(lambda: not dialog.isVisible(), timeout=1000)

    def test_keyboard_shortcuts(self, qtbot):
        """测试键盘快捷键。"""
        # 测试Ctrl+N快捷键
        qtbot.keySequence(self.main_window, "Ctrl+N")

        # 验证新建任务对话框打开
        dialog = self.main_window.findChild(QDialog, "task_create_dialog")
        qtbot.waitUntil(lambda: dialog is not None and dialog.isVisible(), timeout=1000)
```

## 📚 版本控制规范

### 5.1 Git工作流程

#### 分支策略
```bash
# 主要分支
main        # 主分支，包含生产就绪的代码
develop     # 开发分支，包含最新的开发代码

# 功能分支
feature/task-management     # 任务管理功能
feature/script-editor      # 脚本编辑器功能
feature/ui-improvements    # UI改进

# 修复分支
hotfix/critical-bug-fix    # 紧急修复
bugfix/task-execution-fix  # 一般错误修复

# 发布分支
release/v1.0.0            # 版本发布准备

# 分支命名规范
feature/功能描述           # 新功能开发
bugfix/错误描述           # 错误修复
hotfix/紧急修复描述       # 紧急修复
release/版本号            # 发布准备
```

#### 提交信息规范
```bash
# 提交信息格式
<type>(<scope>): <subject>

<body>

<footer>

# 类型说明
feat:     新功能
fix:      错误修复
docs:     文档更新
style:    代码格式化（不影响功能）
refactor: 代码重构
test:     测试相关
chore:    构建过程或辅助工具的变动

# 示例
feat(task): add task scheduling functionality

- Implement cron-based task scheduling
- Add schedule validation
- Update task model with schedule fields

Closes #123

fix(ui): resolve task list refresh issue

The task list was not updating after task creation.
Fixed by adding proper signal-slot connections.

Fixes #456

docs(api): update task service documentation

- Add missing parameter descriptions
- Include usage examples
- Fix typos in method signatures

test(task): add unit tests for task validation

- Test required field validation
- Test data type validation
- Test business rule validation

Coverage increased from 75% to 85%
```

#### Pre-commit钩子配置
```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-merge-conflict
      - id: debug-statements

  - repo: https://github.com/psf/black
    rev: 23.11.0
    hooks:
      - id: black
        language_version: python3
        args: [--line-length=88]

  - repo: https://github.com/pycqa/flake8
    rev: 6.1.0
    hooks:
      - id: flake8
        args: [--max-line-length=88, --extend-ignore=E203,W503]

  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: [--profile=black]

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.7.1
    hooks:
      - id: mypy
        additional_dependencies: [types-all]
        args: [--ignore-missing-imports]

# 安装pre-commit钩子
pip install pre-commit
pre-commit install

# 手动运行所有钩子
pre-commit run --all-files
```

---

**文档版本**: 1.0
**最后更新**: 2024-12-19
**文档状态**: 已完成
