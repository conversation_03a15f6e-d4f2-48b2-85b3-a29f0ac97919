#!/usr/bin/env python3
"""
主应用测试脚本

测试完整的主应用，包括导航、页面切换和数据集成。
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt

from src.ui.main_window import MainWindow
from src.utils.logger import setup_logging

def main():
    """主函数"""
    # 设置日志
    setup_logging()
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("任务管理工具")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("开发团队")
    
    # 创建主窗口
    main_window = MainWindow()
    
    # 显示窗口
    main_window.show()
    
    # 运行应用程序
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
