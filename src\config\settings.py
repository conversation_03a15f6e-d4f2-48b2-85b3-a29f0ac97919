"""
应用程序设置配置

包含应用程序的所有配置项和默认值。
"""

import os
from pathlib import Path
from typing import Dict, Any

# 应用程序基础信息
APP_NAME = "TaskManager"
APP_VERSION = "1.0.0"
APP_AUTHOR = "Development Team"
APP_DESCRIPTION = "自动化任务管理工具"

# 路径配置
# BASE_DIR: 获取当前文件的父目录的父目录的父目录,即项目根目录
BASE_DIR = Path(__file__).parent.parent.parent
# SRC_DIR: 源代码目录,存放所有Python源文件
SRC_DIR = BASE_DIR / "src"
# DATA_DIR: 数据目录,存放数据库文件等数据
DATA_DIR = BASE_DIR / "data"
# LOG_DIR: 日志目录,存放应用程序日志文件
LOG_DIR = BASE_DIR / "logs"
# CONFIG_DIR: 配置目录,存放配置文件
CONFIG_DIR = BASE_DIR / "config"
# RESOURCES_DIR: 资源目录,存放图标、图片等静态资源
RESOURCES_DIR = BASE_DIR / "resources"

# 确保目录存在
DATA_DIR.mkdir(exist_ok=True)
LOG_DIR.mkdir(exist_ok=True)

# 数据库配置
DATABASE_CONFIG = {
    "engine": "sqlite",
    "database": str(DATA_DIR / "task_manager.db"),
    "echo": False,  # 生产环境关闭SQL日志
    "pool_pre_ping": True,
    "pool_recycle": 3600,
    "connect_args": {
        "check_same_thread": False,
        "timeout": 30,
    }
}

# 日志配置
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "standard": {
            "format": "%(asctime)s [%(levelname)s] %(filename)s_%(name)s: %(lineno)d: %(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S"
        },
        "detailed": {
            "format": "%(asctime)s [%(levelname)s] %(name)s:%(lineno)d: %(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S"
        }
    },
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
            "level": "INFO",
            "formatter": "standard",
            "stream": "ext://sys.stdout"
        },
        "file": {
            "class": "logging.handlers.RotatingFileHandler",
            "level": "DEBUG",
            "formatter": "detailed",
            "filename": str(LOG_DIR / "app.log"),
            "maxBytes": 10485760,  # 10MB
            "backupCount": 5,
            "encoding": "utf-8"
        },
        "error_file": {
            "class": "logging.handlers.RotatingFileHandler",
            "level": "ERROR",
            "formatter": "detailed",
            "filename": str(LOG_DIR / "error.log"),
            "maxBytes": 10485760,  # 10MB
            "backupCount": 5,
            "encoding": "utf-8"
        }
    },
    "loggers": {
        "": {  # root logger
            "level": "DEBUG",
            "handlers": ["console", "file", "error_file"],
            "propagate": False
        },
        "sqlalchemy": {
            "level": "WARNING",
            "handlers": ["file"],
            "propagate": False
        }
    }
}

# UI配置
UI_CONFIG = {
    "window": {
        "title": APP_NAME,
        "width": 1200,
        "height": 800,
        "min_width": 1024,
        "min_height": 768,
    },
    "theme": {
        "default": "light",
        "available": ["light", "dark"],
    },
    "font": {
        "family": "Microsoft YaHei UI",
        "size": 9,
    }
}

# 任务执行配置
EXECUTION_CONFIG = {
    "max_concurrent_tasks": 3,
    "default_timeout": 3600,  # 1小时
    "retry_attempts": 3,
    "retry_delay": 5,  # 秒
    "log_retention_days": 30,
}

# 安全配置
SECURITY_CONFIG = {
    "sandbox_enabled": True,
    "allowed_modules": [
        "os", "sys", "json", "datetime", "time", "math", "random",
        "re", "urllib", "pathlib", "csv", "xml", "base64"
    ],
    "restricted_functions": [
        "exec", "eval", "compile", "__import__", "open", "file",
        "input", "raw_input"
    ],
    "max_memory_mb": 512,
    "max_cpu_time": 300,  # 5分钟
}

# 调度配置
SCHEDULER_CONFIG = {
    "timezone": "Asia/Shanghai",
    "max_instances": 1,
    "coalesce": True,
    "misfire_grace_time": 30,
    "job_defaults": {
        "coalesce": False,
        "max_instances": 1,
        "misfire_grace_time": 30,
    }
}

# 通知配置
NOTIFICATION_CONFIG = {
    "desktop_notifications": True,
    "email_notifications": False,
    "webhook_notifications": False,
    "notification_levels": ["ERROR", "WARNING", "INFO"],
}

# 开发配置
DEV_CONFIG = {
    "debug": False,
    "auto_reload": False,
    "profiling": False,
    "mock_data": False,
}

# 环境变量配置
def get_env_config() -> Dict[str, Any]:
    """从环境变量获取配置"""
    return {
        "debug": os.getenv("DEBUG", "false").lower() == "true",
        "log_level": os.getenv("LOG_LEVEL", "INFO").upper(),
        "database_url": os.getenv("DATABASE_URL", DATABASE_CONFIG["database"]),
        "secret_key": os.getenv("SECRET_KEY", "default-secret-key"),
    }

# 合并所有配置
def get_settings() -> Dict[str, Any]:
    """获取完整的应用程序设置"""
    settings = {
        "app": {
            "name": APP_NAME,
            "version": APP_VERSION,
            "author": APP_AUTHOR,
            "description": APP_DESCRIPTION,
        },
        "paths": {
            "base_dir": str(BASE_DIR),
            "src_dir": str(SRC_DIR),
            "data_dir": str(DATA_DIR),
            "log_dir": str(LOG_DIR),
            "config_dir": str(CONFIG_DIR),
            "resources_dir": str(RESOURCES_DIR),
        },
        "database": DATABASE_CONFIG,
        "logging": LOGGING_CONFIG,
        "ui": UI_CONFIG,
        "execution": EXECUTION_CONFIG,
        "security": SECURITY_CONFIG,
        "scheduler": SCHEDULER_CONFIG,
        "notification": NOTIFICATION_CONFIG,
        "dev": DEV_CONFIG,
        "env": get_env_config(),
    }
    
    return settings
