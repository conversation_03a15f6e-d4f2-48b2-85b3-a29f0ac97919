"""
数据库管理模块

提供数据库连接、初始化和迁移功能。
"""

import os
from pathlib import Path
from typing import Optional, Generator
from contextlib import contextmanager
from sqlalchemy import create_engine, Engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool

from ..config.settings import DATABASE_CONFIG
from ..utils.logger import get_logger
from .base import Base

logger = get_logger(__name__)


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        """初始化数据库管理器"""
        self.engine: Optional[Engine] = None
        self.session_factory: Optional[sessionmaker] = None
        self._initialized = False
    
    def initialize(self, database_url: Optional[str] = None) -> None:
        """
        初始化数据库连接
        
        Args:
            database_url: 数据库连接URL，如果为None则使用配置文件中的URL
        """
        if self._initialized:
            logger.warning("数据库已经初始化，跳过重复初始化")
            return
        
        try:
            # 使用提供的URL或配置文件中的URL
            if database_url is None:
                database_url = self._build_database_url()
            
            logger.info(f"初始化数据库连接: {database_url}")
            
            # 创建数据库引擎
            self.engine = create_engine(
                database_url,
                echo=DATABASE_CONFIG.get("echo", False),
                pool_pre_ping=DATABASE_CONFIG.get("pool_pre_ping", True),
                poolclass=StaticPool,
                connect_args=DATABASE_CONFIG.get("connect_args", {})
            )
            
            # 创建会话工厂
            self.session_factory = sessionmaker(
                bind=self.engine,
                autocommit=False,
                autoflush=False
            )
            
            # 测试连接
            self._test_connection()
            
            self._initialized = True
            logger.info("数据库初始化成功")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def _build_database_url(self) -> str:
        """构建数据库连接URL"""
        engine = DATABASE_CONFIG.get("engine", "sqlite")
        database = DATABASE_CONFIG.get("database", "task_manager.db")
        
        if engine == "sqlite":
            # 确保数据库目录存在
            db_path = Path(database)
            db_path.parent.mkdir(parents=True, exist_ok=True)
            return f"sqlite:///{database}"
        else:
            raise ValueError(f"不支持的数据库引擎: {engine}")
    
    def _test_connection(self) -> None:
        """测试数据库连接"""
        try:
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            logger.debug("数据库连接测试成功")
        except Exception as e:
            logger.error(f"数据库连接测试失败: {e}")
            raise
    
    def create_tables(self) -> None:
        """创建所有数据表"""
        if not self._initialized:
            raise RuntimeError("数据库未初始化")
        
        try:
            logger.info("开始创建数据表...")
            Base.metadata.create_all(self.engine)
            logger.info("数据表创建成功")
        except Exception as e:
            logger.error(f"数据表创建失败: {e}")
            raise
    
    def drop_tables(self) -> None:
        """删除所有数据表"""
        if not self._initialized:
            raise RuntimeError("数据库未初始化")
        
        try:
            logger.warning("开始删除数据表...")
            Base.metadata.drop_all(self.engine)
            logger.warning("数据表删除成功")
        except Exception as e:
            logger.error(f"数据表删除失败: {e}")
            raise
    
    def get_session(self) -> Session:
        """
        获取数据库会话
        
        Returns:
            数据库会话对象
        """
        if not self._initialized:
            raise RuntimeError("数据库未初始化")
        
        return self.session_factory()
    
    @contextmanager
    def session_scope(self) -> Generator[Session, None, None]:
        """
        数据库会话上下文管理器
        
        Yields:
            数据库会话对象
        """
        session = self.get_session()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()
    
    def close(self) -> None:
        """关闭数据库连接"""
        if self.engine:
            self.engine.dispose()
            logger.info("数据库连接已关闭")
        
        self._initialized = False
    
    def get_table_info(self) -> dict:
        """
        获取数据表信息
        
        Returns:
            数据表信息字典
        """
        if not self._initialized:
            raise RuntimeError("数据库未初始化")
        
        table_info = {}
        
        try:
            with self.session_scope() as session:
                # 获取所有表名
                if DATABASE_CONFIG.get("engine") == "sqlite":
                    result = session.execute(text(
                        "SELECT name FROM sqlite_master WHERE type='table'"
                    ))
                    tables = [row[0] for row in result]
                    
                    for table_name in tables:
                        # 获取表的行数
                        count_result = session.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                        row_count = count_result.scalar()
                        table_info[table_name] = {"row_count": row_count}
                
        except Exception as e:
            logger.error(f"获取数据表信息失败: {e}")
            raise
        
        return table_info
    
    def backup_database(self, backup_path: str) -> None:
        """
        备份数据库
        
        Args:
            backup_path: 备份文件路径
        """
        if not self._initialized:
            raise RuntimeError("数据库未初始化")
        
        if DATABASE_CONFIG.get("engine") != "sqlite":
            raise NotImplementedError("目前只支持SQLite数据库备份")
        
        try:
            import shutil
            database_path = DATABASE_CONFIG.get("database")
            shutil.copy2(database_path, backup_path)
            logger.info(f"数据库备份成功: {backup_path}")
        except Exception as e:
            logger.error(f"数据库备份失败: {e}")
            raise
    
    def restore_database(self, backup_path: str) -> None:
        """
        恢复数据库
        
        Args:
            backup_path: 备份文件路径
        """
        if not self._initialized:
            raise RuntimeError("数据库未初始化")
        
        if DATABASE_CONFIG.get("engine") != "sqlite":
            raise NotImplementedError("目前只支持SQLite数据库恢复")
        
        try:
            import shutil
            database_path = DATABASE_CONFIG.get("database")
            
            # 关闭当前连接
            self.close()
            
            # 恢复数据库文件
            shutil.copy2(backup_path, database_path)
            
            # 重新初始化
            self.initialize()
            
            logger.info(f"数据库恢复成功: {backup_path}")
        except Exception as e:
            logger.error(f"数据库恢复失败: {e}")
            raise


# 全局数据库管理器实例
db_manager = DatabaseManager()


def init_database(database_url: Optional[str] = None) -> None:
    """
    初始化数据库
    
    Args:
        database_url: 数据库连接URL
    """
    db_manager.initialize(database_url)
    db_manager.create_tables()


def get_session() -> Session:
    """
    获取数据库会话
    
    Returns:
        数据库会话对象
    """
    return db_manager.get_session()


@contextmanager
def session_scope() -> Generator[Session, None, None]:
    """
    数据库会话上下文管理器
    
    Yields:
        数据库会话对象
    """
    with db_manager.session_scope() as session:
        yield session


def close_database() -> None:
    """关闭数据库连接"""
    db_manager.close()
