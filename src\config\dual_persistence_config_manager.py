"""
双重持久化配置管理器

实现配置数据在文件和数据库之间的双重持久化机制，确保数据一致性和可靠性。
"""

import json
import logging
from typing import Dict, Any, Optional, Tuple, Union
from datetime import datetime
from pathlib import Path

from .config_manager import ConfigManager
from ..services.config_service import ConfigService
from ..models.database import session_scope
from ..models.config import Config, ConfigType, ConfigScope


class DualPersistenceConfigManager(ConfigManager):
    """
    双重持久化配置管理器
    
    扩展ConfigManager，增加数据库持久化功能，实现文件和数据库的双重存储。
    """
    
    def __init__(self, config_file: Optional[Path] = None):
        """
        初始化双重持久化配置管理器
        
        Args:
            config_file: 配置文件路径，如果为None则使用默认路径
        """
        super().__init__(config_file)
        self.config_service = ConfigService()
        self.logger = logging.getLogger(__name__)
        
        # 双重持久化配置
        self._enable_dual_persistence = True
        self._database_priority = True  # 数据库优先
        self._conflict_resolution = "timestamp"  # 冲突解决策略
        
        # 初始化时加载配置
        self._load_dual_config()
    
    def _load_dual_config(self) -> None:
        """
        加载双重配置，优先级：数据库 -> 文件 -> 默认值
        """
        try:
            self.logger.info("开始加载双重持久化配置")
            
            # 1. 尝试从数据库加载
            database_config = self._load_from_database()
            
            # 2. 从文件加载
            file_config = self._load_from_file()
            
            # 3. 合并配置，处理冲突
            merged_config = self._merge_configs(database_config, file_config)
            
            # 4. 更新内存配置
            self._config = merged_config
            
            # 5. 同步配置（如果需要）
            self._sync_configs_if_needed(database_config, file_config)
            
            self.logger.info("双重持久化配置加载完成")
            
        except Exception as e:
            self.logger.error(f"加载双重持久化配置失败: {e}")
            # 回退到父类的文件加载
            super()._load_config()
    
    def _load_from_database(self) -> Dict[str, Any]:
        """
        从数据库加载配置
        
        Returns:
            数据库中的配置字典
        """
        try:
            database_config = {}
            
            with session_scope() as session:
                configs = session.query(Config).filter(
                    Config.is_deleted == False
                ).all()
                
                for config in configs:
                    # 构建嵌套键路径
                    key_parts = config.key.split('.')
                    current = database_config
                    
                    # 创建嵌套结构
                    for part in key_parts[:-1]:
                        if part not in current:
                            current[part] = {}
                        current = current[part]
                    
                    # 设置最终值
                    current[key_parts[-1]] = config.get_effective_value()
            
            self.logger.info(f"从数据库加载了 {len(configs)} 个配置项")
            return database_config
            
        except Exception as e:
            self.logger.warning(f"从数据库加载配置失败: {e}")
            return {}
    
    def _load_from_file(self) -> Dict[str, Any]:
        """
        从文件加载配置
        
        Returns:
            文件中的配置字典
        """
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                self.logger.info(f"从文件加载了配置: {self.config_file}")
                return file_config
            else:
                self.logger.info("配置文件不存在，使用空配置")
                return {}
                
        except Exception as e:
            self.logger.warning(f"从文件加载配置失败: {e}")
            return {}
    
    def _merge_configs(self, database_config: Dict[str, Any], 
                      file_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        合并数据库和文件配置，处理冲突
        
        Args:
            database_config: 数据库配置
            file_config: 文件配置
            
        Returns:
            合并后的配置
        """
        # 从默认配置开始
        merged_config = self._default_config.copy()
        
        if self._database_priority:
            # 数据库优先：默认值 -> 文件 -> 数据库
            self._deep_merge(merged_config, file_config)
            self._deep_merge(merged_config, database_config)
        else:
            # 文件优先：默认值 -> 数据库 -> 文件
            self._deep_merge(merged_config, database_config)
            self._deep_merge(merged_config, file_config)
        
        return merged_config
    
    def _deep_merge(self, target: Dict[str, Any], source: Dict[str, Any]) -> None:
        """
        深度合并字典
        
        Args:
            target: 目标字典（会被修改）
            source: 源字典
        """
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._deep_merge(target[key], value)
            else:
                target[key] = value
    
    def _sync_configs_if_needed(self, database_config: Dict[str, Any], 
                               file_config: Dict[str, Any]) -> None:
        """
        如果需要，同步配置数据
        
        Args:
            database_config: 数据库配置
            file_config: 文件配置
        """
        try:
            # 检查是否需要同步
            if not database_config and file_config:
                # 数据库为空，从文件同步到数据库
                self.logger.info("数据库配置为空，从文件同步到数据库")
                self._sync_file_to_database(file_config)
                
            elif database_config and not file_config:
                # 文件为空，从数据库同步到文件
                self.logger.info("文件配置为空，从数据库同步到文件")
                self._sync_database_to_file(database_config)
                
        except Exception as e:
            self.logger.warning(f"配置同步失败: {e}")
    
    def _sync_file_to_database(self, file_config: Dict[str, Any]) -> None:
        """
        将文件配置同步到数据库
        
        Args:
            file_config: 文件配置
        """
        try:
            flat_config = self._flatten_config(file_config)
            
            for key, value in flat_config.items():
                self.config_service.set_config_value(
                    key=key,
                    value=value,
                    target="database"
                )
            
            self.logger.info(f"已将 {len(flat_config)} 个配置项同步到数据库")
            
        except Exception as e:
            self.logger.error(f"同步文件到数据库失败: {e}")
    
    def _sync_database_to_file(self, database_config: Dict[str, Any]) -> None:
        """
        将数据库配置同步到文件
        
        Args:
            database_config: 数据库配置
        """
        try:
            # 获取与默认配置的差异
            diff_config = self._get_diff_config(database_config, self._default_config)
            
            # 保存到文件
            self._save_to_file(diff_config)
            
            self.logger.info("已将数据库配置同步到文件")
            
        except Exception as e:
            self.logger.error(f"同步数据库到文件失败: {e}")
    
    def _flatten_config(self, config: Dict[str, Any], prefix: str = "") -> Dict[str, Any]:
        """
        扁平化配置字典
        
        Args:
            config: 嵌套配置字典
            prefix: 键前缀
            
        Returns:
            扁平化的配置字典
        """
        flat = {}
        
        for key, value in config.items():
            full_key = f"{prefix}.{key}" if prefix else key
            
            if isinstance(value, dict):
                flat.update(self._flatten_config(value, full_key))
            else:
                flat[full_key] = value
        
        return flat
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值，支持双重持久化
        
        Args:
            key: 配置键
            default: 默认值
            
        Returns:
            配置值
        """
        try:
            # 首先尝试从内存获取
            value = super().get(key, None)
            
            if value is not None:
                return value
            
            # 如果内存中没有，尝试从数据库获取
            if self._enable_dual_persistence:
                db_value = self.config_service.get_config_value(key, source="database")
                if db_value is not None:
                    # 更新内存缓存
                    self.set(key, db_value, save=False)
                    return db_value
            
            # 返回默认值
            return default

        except Exception as e:
            self.logger.warning(f"获取配置值失败 {key}: {e}")
            return default

    def set(self, key: str, value: Any, save: bool = True) -> bool:
        """
        设置配置值，支持双重持久化

        Args:
            key: 配置键
            value: 配置值
            save: 是否立即保存

        Returns:
            是否设置成功
        """
        try:
            # 首先设置内存值
            success = super().set(key, value, save=False)

            if not success:
                return False

            # 如果需要保存，执行双重持久化
            if save and self._enable_dual_persistence:
                return self._dual_save_config(key, value)
            elif save:
                # 只保存到文件
                return super().save_config()

            return True

        except Exception as e:
            self.logger.error(f"设置配置值失败 {key}: {e}")
            return False

    def _dual_save_config(self, key: Optional[str] = None, value: Any = None) -> bool:
        """
        双重持久化保存配置

        Args:
            key: 特定配置键（如果只保存单个配置）
            value: 配置值

        Returns:
            是否保存成功
        """
        try:
            self.logger.info("开始双重持久化保存配置")

            # 准备保存数据
            if key and value is not None:
                # 单个配置保存
                save_data = {key: value}
            else:
                # 全量配置保存
                save_data = self._get_changed_configs()

            if not save_data:
                self.logger.info("没有配置变更，跳过保存")
                return True

            # 原子性双重保存
            return self._atomic_dual_save(save_data)

        except Exception as e:
            self.logger.error(f"双重持久化保存失败: {e}")
            return False

    def _get_changed_configs(self) -> Dict[str, Any]:
        """
        获取已变更的配置

        Returns:
            变更的配置字典
        """
        # 获取与默认配置的差异
        return self._get_diff_config(self._config, self._default_config)

    def _atomic_dual_save(self, save_data: Dict[str, Any]) -> bool:
        """
        原子性双重保存

        Args:
            save_data: 要保存的配置数据

        Returns:
            是否保存成功
        """
        file_backup = None
        database_changes = []

        try:
            self.logger.info(f"开始原子性双重保存 {len(save_data)} 个配置项")

            # 1. 备份当前文件
            file_backup = self._backup_config_file()

            # 2. 保存到文件
            file_success = self._save_to_file_atomic(save_data)
            if not file_success:
                raise Exception("文件保存失败")

            # 3. 保存到数据库（事务性）
            database_success, database_changes = self._save_to_database_atomic(save_data)
            if not database_success:
                raise Exception("数据库保存失败")

            # 4. 清理备份
            if file_backup and file_backup.exists():
                file_backup.unlink()

            self.logger.info("原子性双重保存成功")
            return True

        except Exception as e:
            self.logger.error(f"原子性双重保存失败: {e}")

            # 回滚操作
            self._rollback_dual_save(file_backup, database_changes)
            return False

    def _backup_config_file(self) -> Optional[Path]:
        """
        备份配置文件

        Returns:
            备份文件路径
        """
        try:
            if self.config_file.exists():
                backup_file = self.config_file.with_suffix('.backup')
                backup_file.write_bytes(self.config_file.read_bytes())
                return backup_file
            return None

        except Exception as e:
            self.logger.warning(f"备份配置文件失败: {e}")
            return None

    def _save_to_file_atomic(self, save_data: Dict[str, Any]) -> bool:
        """
        原子性保存到文件

        Args:
            save_data: 要保存的数据

        Returns:
            是否保存成功
        """
        try:
            # 获取完整的差异配置
            diff_config = self._get_diff_config(self._config, self._default_config)

            # 保存到文件
            return self._save_to_file(diff_config)

        except Exception as e:
            self.logger.error(f"原子性文件保存失败: {e}")
            return False

    def _save_to_database_atomic(self, save_data: Dict[str, Any]) -> Tuple[bool, list]:
        """
        原子性保存到数据库

        Args:
            save_data: 要保存的数据

        Returns:
            (是否成功, 变更记录列表)
        """
        changes = []

        try:
            # 扁平化配置数据
            flat_data = self._flatten_config(save_data)

            # 批量保存到数据库
            for key, value in flat_data.items():
                success = self.config_service.set_config_value(
                    key=key,
                    value=value,
                    target="database"
                )

                if success:
                    changes.append(key)
                else:
                    raise Exception(f"保存配置项失败: {key}")

            self.logger.info(f"数据库保存成功: {len(changes)} 个配置项")
            return True, changes

        except Exception as e:
            self.logger.error(f"原子性数据库保存失败: {e}")
            return False, changes

    def _rollback_dual_save(self, file_backup: Optional[Path],
                           database_changes: list) -> None:
        """
        回滚双重保存操作

        Args:
            file_backup: 文件备份路径
            database_changes: 数据库变更记录
        """
        try:
            self.logger.warning("开始回滚双重保存操作")

            # 回滚文件
            if file_backup and file_backup.exists():
                if self.config_file.exists():
                    self.config_file.unlink()
                file_backup.rename(self.config_file)
                self.logger.info("文件回滚完成")

            # 回滚数据库（这里简化处理，实际应该在事务中处理）
            if database_changes:
                self.logger.warning(f"需要手动检查数据库回滚: {database_changes}")

        except Exception as e:
            self.logger.error(f"回滚操作失败: {e}")

    def save_config(self) -> bool:
        """
        保存配置（重写父类方法以支持双重持久化）

        Returns:
            是否保存成功
        """
        if self._enable_dual_persistence:
            return self._dual_save_config()
        else:
            return super().save_config()

    def enable_dual_persistence(self, enable: bool = True) -> None:
        """
        启用或禁用双重持久化

        Args:
            enable: 是否启用
        """
        self._enable_dual_persistence = enable
        self.logger.info(f"双重持久化已{'启用' if enable else '禁用'}")

    def set_database_priority(self, priority: bool = True) -> None:
        """
        设置数据库优先级

        Args:
            priority: 是否数据库优先
        """
        self._database_priority = priority
        self.logger.info(f"配置加载优先级: {'数据库优先' if priority else '文件优先'}")

    def get_persistence_status(self) -> Dict[str, Any]:
        """
        获取持久化状态信息

        Returns:
            持久化状态字典
        """
        return {
            "dual_persistence_enabled": self._enable_dual_persistence,
            "database_priority": self._database_priority,
            "conflict_resolution": self._conflict_resolution,
            "config_file_exists": self.config_file.exists(),
            "database_available": self._check_database_availability()
        }

    def _check_database_availability(self) -> bool:
        """
        检查数据库可用性

        Returns:
            数据库是否可用
        """
        try:
            return self.config_service.get_config_value("app.name") is not None
        except:
            return False
