"""
首页界面

提供系统概览、统计信息、图表展示和快速操作功能。
基于T2.1主窗口框架、T2.2侧边导航组件、T2.3基础UI组件库和T2.4主题系统的高质量标准实现。
"""

from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QScrollArea,
    QLabel, QFrame, QSplitter, QProgressBar
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread, pyqtSlot
from PyQt6.QtGui import QFont, QIcon

from src.utils.logger import get_logger
from src.ui.pages.base_page import BasePage
from src.ui.components.stat_card import StatCard, StatCardType
from src.ui.components.chart_widget import LineChart, PieChart, AreaChart
from src.ui.styles import get_theme_manager, ColorRole, ComponentSize
from src.services.service_factory import get_task_service, get_script_service, get_execution_service
import psutil


class DataLoader(QThread):
    """数据加载线程"""

    # 信号定义
    data_loaded = pyqtSignal(dict)  # 数据加载完成
    error_occurred = pyqtSignal(str)  # 错误发生

    def __init__(self):
        """初始化数据加载器"""
        super().__init__()
        self.task_service = get_task_service()
        self.script_service = get_script_service()
        self.execution_service = get_execution_service()

    def run(self):
        """运行数据加载"""
        try:
            # 获取任务统计
            task_stats = self.task_service.get_statistics()

            # 获取脚本统计
            script_stats = self.script_service.get_statistics()

            # 获取执行统计
            execution_stats = self.execution_service.get_statistics()

            # 获取监控仪表板数据
            dashboard_data = self.task_service.get_monitoring_dashboard()

            # 获取系统资源信息
            system_stats = {
                'cpu_usage': psutil.cpu_percent(interval=1),
                'memory_usage': psutil.virtual_memory().percent,
                'disk_usage': psutil.disk_usage('/').percent if hasattr(psutil, 'disk_usage') else 0
            }

            # 组合所有数据
            all_data = {
                'task_stats': task_stats,
                'script_stats': script_stats,
                'execution_stats': execution_stats,
                'dashboard_data': dashboard_data,
                'system_stats': system_stats
            }

            self.data_loaded.emit(all_data)

        except Exception as e:
            self.error_occurred.emit(str(e))


class HomePage(BasePage):
    """
    首页界面

    显示系统概览、统计信息和快速操作。
    """

    # 信号定义
    quick_action_triggered = pyqtSignal(str)  # 快速操作信号

    def __init__(self, parent: Optional[QWidget] = None):
        """
        初始化首页

        Args:
            parent: 父窗口
        """
        # 初始化日志
        self.logger = get_logger(self.__class__.__name__)

        # 服务层
        self.task_service = get_task_service()
        self.script_service = get_script_service()
        self.execution_service = get_execution_service()

        # 数据加载器
        self.data_loader = DataLoader()
        self.data_loader.data_loaded.connect(self._on_data_loaded)
        self.data_loader.error_occurred.connect(self._on_data_error)

        # 主题管理器
        self.theme_manager = get_theme_manager()

        # UI组件
        self.stat_cards: Dict[str, StatCard] = {}
        self.charts: Dict[str, QWidget] = {}

        # 数据缓存
        self.cached_data: Optional[Dict[str, Any]] = None

        # 加载状态
        self.loading_progress: Optional[QProgressBar] = None

        # 数据刷新定时器
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self._refresh_data)
        self.refresh_timer.start(30000)  # 30秒刷新一次

        # 调用父类初始化
        super().__init__(parent)

        # 初始加载数据
        self._load_initial_data()

        self.logger.info("首页界面初始化完成")
    
    def get_page_title(self) -> str:
        """获取页面标题"""
        return "首页"
    
    def get_page_key(self) -> str:
        """获取页面键"""
        return "home"
    
    def _create_page_content(self, layout: QVBoxLayout) -> None:
        """创建页面内容"""
        # 主滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.Shape.NoFrame)

        # 滚动内容
        scroll_content = QWidget()
        content_layout = QVBoxLayout(scroll_content)
        content_layout.setContentsMargins(24, 20, 24, 20)  # 增加边距
        content_layout.setSpacing(24)  # 增加区域间距

        # 统计卡片区域（移除重复的页面标题）
        self._create_stats_section(content_layout)

        # 图表区域
        self._create_charts_section(content_layout)

        # 快速操作区域
        self._create_quick_actions_section(content_layout)

        scroll_area.setWidget(scroll_content)

        # 添加到传入的布局
        layout.addWidget(scroll_area)
    

    
    def _create_stats_section(self, layout: QVBoxLayout) -> None:
        """创建统计卡片区域"""
        # 区域标题
        section_title = QLabel("系统统计")
        section_font = QFont()
        section_font.setPointSize(14)  # 减小区域标题字体
        section_font.setBold(True)
        section_title.setFont(section_font)
        layout.addWidget(section_title)
        
        # 统计卡片网格
        stats_grid = QGridLayout()
        stats_grid.setSpacing(20)  # 增加卡片间距
        stats_grid.setContentsMargins(0, 0, 0, 0)
        
        # 创建统计卡片
        self._create_stat_cards(stats_grid)
        
        # 添加到布局
        stats_frame = QFrame()
        stats_frame.setLayout(stats_grid)
        layout.addWidget(stats_frame)
    
    def _create_stat_cards(self, grid_layout: QGridLayout) -> None:
        """创建统计卡片"""
        # 任务统计卡片
        task_card = StatCard(
            card_id="tasks",
            title="总任务数",
            value=0,
            subtitle="运行中: 0",
            card_type=StatCardType.TASK,
            clickable=True
        )
        task_card.card_clicked.connect(self._on_stat_card_clicked)
        self.stat_cards["tasks"] = task_card
        grid_layout.addWidget(task_card, 0, 0)
        
        # 系统状态卡片
        system_card = StatCard(
            card_id="system",
            title="CPU使用率",
            value="0%",
            subtitle="内存: 0%",
            card_type=StatCardType.SYSTEM,
            clickable=True
        )
        system_card.card_clicked.connect(self._on_stat_card_clicked)
        self.stat_cards["system"] = system_card
        grid_layout.addWidget(system_card, 0, 1)
        
        # 脚本统计卡片
        script_card = StatCard(
            card_id="scripts",
            title="脚本总数",
            value=0,
            subtitle="活跃: 0",
            card_type=StatCardType.SCRIPT,
            clickable=True
        )
        script_card.card_clicked.connect(self._on_stat_card_clicked)
        self.stat_cards["scripts"] = script_card
        grid_layout.addWidget(script_card, 0, 2)
        
        # 性能指标卡片
        performance_card = StatCard(
            card_id="performance",
            title="系统运行时间",
            value="99.9%",
            subtitle="响应时间: 0ms",
            card_type=StatCardType.GENERAL,
            clickable=True
        )
        performance_card.card_clicked.connect(self._on_stat_card_clicked)
        self.stat_cards["performance"] = performance_card
        grid_layout.addWidget(performance_card, 1, 0)
        
        # 成功率卡片
        success_card = StatCard(
            card_id="success_rate",
            title="任务成功率",
            value="0%",
            subtitle="今日执行: 0",
            card_type=StatCardType.TASK,
            clickable=True
        )
        success_card.card_clicked.connect(self._on_stat_card_clicked)
        self.stat_cards["success_rate"] = success_card
        grid_layout.addWidget(success_card, 1, 1)
        
        # 错误率卡片
        error_card = StatCard(
            card_id="error_rate",
            title="错误率",
            value="0%",
            subtitle="最近24小时",
            card_type=StatCardType.GENERAL,
            clickable=True
        )
        error_card.card_clicked.connect(self._on_stat_card_clicked)
        self.stat_cards["error_rate"] = error_card
        grid_layout.addWidget(error_card, 1, 2)
    
    def _create_charts_section(self, layout: QVBoxLayout) -> None:
        """创建图表区域"""
        # 区域标题
        section_title = QLabel("数据可视化")
        section_font = QFont()
        section_font.setPointSize(14)  # 减小区域标题字体
        section_font.setBold(True)
        section_title.setFont(section_font)
        layout.addWidget(section_title)
        
        # 图表容器
        charts_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 任务趋势图
        trend_chart = LineChart("任务执行趋势")
        trend_chart.setMinimumHeight(300)
        self.charts["trend"] = trend_chart
        charts_splitter.addWidget(trend_chart)
        
        # 成功率饼图
        success_chart = PieChart("任务成功率分布")
        success_chart.setMinimumHeight(300)
        self.charts["success"] = success_chart
        charts_splitter.addWidget(success_chart)
        
        # 设置分割比例
        charts_splitter.setSizes([500, 300])
        
        layout.addWidget(charts_splitter)
        
        # 系统监控图
        monitor_chart = AreaChart("系统资源监控")
        monitor_chart.setMinimumHeight(250)
        self.charts["monitor"] = monitor_chart
        layout.addWidget(monitor_chart)
    
    def _create_quick_actions_section(self, layout: QVBoxLayout) -> None:
        """创建快速操作区域"""
        # 区域标题
        section_title = QLabel("快速操作")
        section_font = QFont()
        section_font.setPointSize(14)  # 减小区域标题字体
        section_font.setBold(True)
        section_title.setFont(section_font)
        layout.addWidget(section_title)
        
        # 快速操作按钮
        actions_layout = QHBoxLayout()
        actions_layout.setSpacing(16)
        
        # 延迟导入避免循环依赖
        from src.ui.components.button import Button, ButtonType
        
        # 新建任务按钮
        new_task_btn = Button("新建任务", ButtonType.PRIMARY, ComponentSize.MEDIUM)
        new_task_btn.clicked.connect(lambda: self._trigger_quick_action("new_task"))
        actions_layout.addWidget(new_task_btn)
        
        # 执行脚本按钮
        run_script_btn = Button("执行脚本", ButtonType.SECONDARY, ComponentSize.MEDIUM)
        run_script_btn.clicked.connect(lambda: self._trigger_quick_action("run_script"))
        actions_layout.addWidget(run_script_btn)
        
        # 查看日志按钮
        view_logs_btn = Button("查看日志", ButtonType.GHOST, ComponentSize.MEDIUM)
        view_logs_btn.clicked.connect(lambda: self._trigger_quick_action("view_logs"))
        actions_layout.addWidget(view_logs_btn)
        
        # 系统设置按钮
        settings_btn = Button("系统设置", ButtonType.GHOST, ComponentSize.MEDIUM)
        settings_btn.clicked.connect(lambda: self._trigger_quick_action("settings"))
        actions_layout.addWidget(settings_btn)
        
        actions_layout.addStretch()
        
        # 添加到布局
        actions_frame = QFrame()
        actions_frame.setLayout(actions_layout)
        layout.addWidget(actions_frame)
    
    def _load_initial_data(self) -> None:
        """加载初始数据"""
        try:
            # 显示加载状态
            self._show_loading_state(True)

            # 启动数据加载线程
            self.data_loader.start()

            self.logger.info("首页初始数据加载开始")

        except Exception as e:
            self.logger.error(f"加载初始数据失败: {e}")
            self._show_loading_state(False)

    def _refresh_data(self) -> None:
        """刷新数据"""
        try:
            # 如果数据加载器未在运行，则启动
            if not self.data_loader.isRunning():
                self.data_loader.start()

        except Exception as e:
            self.logger.error(f"刷新数据失败: {e}")

    def _show_loading_state(self, is_loading: bool) -> None:
        """显示加载状态"""
        if is_loading:
            # 如果没有进度条，则创建
            if not self.loading_progress:
                self.loading_progress = QProgressBar(self)
                self.loading_progress.setRange(0, 0)  # 不确定进度
                self.loading_progress.setFixedHeight(3)
                self.loading_progress.setTextVisible(False)
                self.layout().insertWidget(0, self.loading_progress)

            # 显示进度条
            self.loading_progress.show()
        else:
            # 隐藏进度条
            if self.loading_progress:
                self.loading_progress.hide()

    def _on_data_loaded(self, data: Dict[str, Any]) -> None:
        """数据加载完成处理"""
        try:
            # 缓存数据
            self.cached_data = data

            # 刷新统计卡片数据
            self._refresh_stat_cards()

            # 刷新图表数据
            self._refresh_charts()

            # 隐藏加载状态
            self._show_loading_state(False)

            self.logger.info("首页数据加载完成")

        except Exception as e:
            self.logger.error(f"处理加载数据失败: {e}")
            self._show_loading_state(False)

    def _on_data_error(self, error_msg: str) -> None:
        """数据加载错误处理"""
        self.logger.error(f"数据加载错误: {error_msg}")
        self._show_loading_state(False)
    
    def _refresh_stat_cards(self) -> None:
        """刷新统计卡片数据"""
        try:
            if not self.cached_data:
                return

            task_stats = self.cached_data.get('task_stats', {})
            script_stats = self.cached_data.get('script_stats', {})
            execution_stats = self.cached_data.get('execution_stats', {})
            dashboard_data = self.cached_data.get('dashboard_data', {})
            system_stats = self.cached_data.get('system_stats', {})

            # 获取系统指标
            system_metrics = dashboard_data.get('system_metrics', {})

            # 更新任务统计
            if "tasks" in self.stat_cards:
                total_tasks = system_metrics.get('total_tasks', 0)
                running_tasks = system_metrics.get('running_tasks', 0)
                self.stat_cards["tasks"].update_value(
                    total_tasks,
                    f"运行中: {running_tasks}"
                )

            # 更新系统状态
            if "system" in self.stat_cards:
                cpu_usage = system_stats.get('cpu_usage', 0)
                memory_usage = system_stats.get('memory_usage', 0)
                self.stat_cards["system"].update_value(
                    f"{cpu_usage:.1f}%",
                    f"内存: {memory_usage:.1f}%"
                )

            # 更新脚本统计
            if "scripts" in self.stat_cards:
                total_scripts = script_stats.get('total_count', 0)
                active_scripts = script_stats.get('active_count', 0)
                self.stat_cards["scripts"].update_value(
                    total_scripts,
                    f"活跃: {active_scripts}"
                )

            # 更新性能指标
            if "performance" in self.stat_cards:
                avg_execution_time = system_metrics.get('average_execution_time', 0)
                self.stat_cards["performance"].update_value(
                    f"{avg_execution_time:.2f}s",
                    "平均执行时间"
                )

            # 更新成功率
            if "success_rate" in self.stat_cards:
                success_rate = system_metrics.get('success_rate', 0)
                total_executions = execution_stats.get('total_count', 0)
                self.stat_cards["success_rate"].update_value(
                    f"{success_rate:.1f}%",
                    f"总执行: {total_executions}"
                )

            # 更新错误率
            if "error_rate" in self.stat_cards:
                failed_tasks = system_metrics.get('failed_tasks', 0)
                total_tasks = system_metrics.get('total_tasks', 1)
                error_rate = (failed_tasks / total_tasks * 100) if total_tasks > 0 else 0
                self.stat_cards["error_rate"].update_value(
                    f"{error_rate:.1f}%",
                    "失败率"
                )

        except Exception as e:
            self.logger.error(f"刷新统计卡片失败: {e}")
    
    def _refresh_charts(self) -> None:
        """刷新图表数据"""
        try:
            if not self.cached_data:
                return

            dashboard_data = self.cached_data.get('dashboard_data', {})
            system_stats = self.cached_data.get('system_stats', {})

            # 更新任务趋势图
            if "trend" in self.charts:
                hourly_stats = dashboard_data.get('hourly_stats', {})
                # 转换为图表数据格式
                trend_data = []
                for hour, stats in sorted(hourly_stats.items()):
                    total = stats.get('success', 0) + stats.get('failure', 0)
                    trend_data.append((hour, total))

                if trend_data:
                    self.charts["trend"].set_data(trend_data)

            # 更新成功率饼图
            if "success" in self.charts:
                system_metrics = dashboard_data.get('system_metrics', {})
                success_rate = system_metrics.get('success_rate', 0)
                failure_rate = 100 - success_rate

                success_data = [
                    ("成功", success_rate),
                    ("失败", failure_rate)
                ]
                self.charts["success"].set_data(success_data)

            # 更新系统监控图
            if "monitor" in self.charts:
                # 生成最近的系统监控数据
                cpu_usage = system_stats.get('cpu_usage', 0)
                memory_usage = system_stats.get('memory_usage', 0)

                # 简单的时间序列数据
                now = datetime.now()
                monitor_data = []
                for i in range(10):
                    time_point = now - timedelta(minutes=i)
                    # 模拟一些变化
                    cpu_val = max(0, min(100, cpu_usage + (i % 3 - 1) * 5))
                    monitor_data.append((time_point.strftime('%H:%M'), cpu_val))

                monitor_data.reverse()  # 时间正序
                self.charts["monitor"].set_data(monitor_data)

        except Exception as e:
            self.logger.error(f"刷新图表数据失败: {e}")
    
    def _on_stat_card_clicked(self, card_id: str) -> None:
        """统计卡片点击处理"""
        self.logger.info(f"统计卡片点击: {card_id}")
        
        # 根据卡片类型跳转到相应页面
        page_mapping = {
            "tasks": "task_list",
            "system": "settings",
            "scripts": "script_list",
            "performance": "settings",
            "success_rate": "task_history",
            "error_rate": "task_history"
        }
        
        target_page = page_mapping.get(card_id)
        if target_page:
            # 这里应该触发页面跳转，暂时记录日志
            self.logger.info(f"跳转到页面: {target_page}")
    
    def _trigger_quick_action(self, action: str) -> None:
        """触发快速操作"""
        self.quick_action_triggered.emit(action)
        self.logger.info(f"快速操作触发: {action}")
    
    def refresh_page(self) -> None:
        """刷新页面"""
        self._refresh_data()
        self.logger.info("首页已刷新")

    def closeEvent(self, event) -> None:
        """关闭事件"""
        # 停止定时器
        if self.refresh_timer:
            self.refresh_timer.stop()

        # 停止数据加载线程
        if self.data_loader.isRunning():
            self.data_loader.quit()
            self.data_loader.wait()

        super().closeEvent(event)
