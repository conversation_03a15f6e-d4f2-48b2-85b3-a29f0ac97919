"""
基础DAO类

提供通用的数据访问操作方法。
"""

from typing import Any, Dict, List, Optional, Type, Union, Generic, TypeVar
from abc import ABC, abstractmethod
from contextlib import contextmanager
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from sqlalchemy import and_, or_

from ..models.base import BaseModel
from ..models.database import session_scope, get_session
from ..utils.logger import get_logger
from .exceptions import (
    DAOException, EntityNotFoundError, DuplicateEntityError,
    ValidationError, DatabaseConnectionError, TransactionError,
    QueryError, DataIntegrityError
)
from .query_builder import QueryBuilder

# 泛型类型变量
T = TypeVar('T', bound=BaseModel)

logger = get_logger(__name__)


class BaseDAO(Generic[T], ABC):
    """
    基础DAO类
    
    提供通用的CRUD操作和查询功能。
    """
    
    def __init__(self, model_class: Type[T]):
        """
        初始化BaseDAO
        
        Args:
            model_class: 模型类
        """
        self.model_class = model_class
        self.model_name = model_class.__name__
    
    @contextmanager
    def get_session(self):
        """
        获取数据库会话上下文管理器
        
        Yields:
            数据库会话
        """
        try:
            with session_scope() as session:
                yield session
        except SQLAlchemyError as e:
            logger.error(f"Database session error in {self.model_name}DAO: {e}")
            raise DatabaseConnectionError(f"get_session for {self.model_name}", e)
    
    def create(self, **kwargs) -> T:
        """
        创建新记录
        
        Args:
            **kwargs: 模型字段值
            
        Returns:
            创建的模型实例
            
        Raises:
            ValidationError: 数据验证失败
            DuplicateEntityError: 重复实体
            DAOException: 其他DAO异常
        """
        try:
            with self.get_session() as session:
                # 验证数据
                self._validate_create_data(kwargs)
                
                # 创建实例
                instance = self.model_class(**kwargs)
                
                # 保存到数据库
                session.add(instance)
                session.flush()  # 获取ID但不提交
                
                # 刷新实例以获取最新数据
                session.refresh(instance)

                # 确保所有属性都已加载，然后分离
                self._ensure_attributes_loaded(session, instance)
                session.expunge(instance)

                logger.debug(f"Created {self.model_name} with id: {instance.id}")
                return instance
                
        except IntegrityError as e:
            logger.error(f"Integrity error creating {self.model_name}: {e}")
            raise DuplicateEntityError(self.model_name, "unknown", "unknown")
        except Exception as e:
            logger.error(f"Error creating {self.model_name}: {e}")
            raise DAOException(f"Failed to create {self.model_name}", str(e))
    
    def get_by_id(self, entity_id: Any) -> Optional[T]:
        """
        根据ID获取记录
        
        Args:
            entity_id: 实体ID
            
        Returns:
            模型实例或None
        """
        try:
            with self.get_session() as session:
                instance = session.query(self.model_class).filter(
                    self.model_class.id == entity_id
                ).first()
                
                if instance:
                    logger.debug(f"Found {self.model_name} with id: {entity_id}")
                else:
                    logger.debug(f"{self.model_name} with id {entity_id} not found")
                
                return instance
                
        except Exception as e:
            logger.error(f"Error getting {self.model_name} by id {entity_id}: {e}")
            raise DAOException(f"Failed to get {self.model_name} by id", str(e))
    
    def get_by_id_or_raise(self, entity_id: Any) -> T:
        """
        根据ID获取记录，如果不存在则抛出异常
        
        Args:
            entity_id: 实体ID
            
        Returns:
            模型实例
            
        Raises:
            EntityNotFoundError: 实体未找到
        """
        instance = self.get_by_id(entity_id)
        if instance is None:
            raise EntityNotFoundError(self.model_name, entity_id)
        return instance
    
    def update(self, entity_id: Any, **kwargs) -> T:
        """
        更新记录
        
        Args:
            entity_id: 实体ID
            **kwargs: 更新的字段值
            
        Returns:
            更新后的模型实例
            
        Raises:
            EntityNotFoundError: 实体未找到
            ValidationError: 数据验证失败
        """
        try:
            with self.get_session() as session:
                # 获取实例
                instance = session.query(self.model_class).filter(
                    self.model_class.id == entity_id
                ).first()
                
                if instance is None:
                    raise EntityNotFoundError(self.model_name, entity_id)
                
                # 验证更新数据
                self._validate_update_data(instance, kwargs)
                
                # 更新字段
                for key, value in kwargs.items():
                    if hasattr(instance, key):
                        setattr(instance, key, value)
                
                session.flush()
                session.refresh(instance)

                # 确保所有属性都已加载，然后分离
                self._ensure_attributes_loaded(session, instance)
                session.expunge(instance)

                logger.debug(f"Updated {self.model_name} with id: {entity_id}")
                return instance
                
        except EntityNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Error updating {self.model_name} with id {entity_id}: {e}")
            raise DAOException(f"Failed to update {self.model_name}", str(e))
    
    def delete(self, entity_id: Any, soft_delete: bool = True) -> bool:
        """
        删除记录
        
        Args:
            entity_id: 实体ID
            soft_delete: 是否软删除
            
        Returns:
            是否删除成功
            
        Raises:
            EntityNotFoundError: 实体未找到
        """
        try:
            with self.get_session() as session:
                instance = session.query(self.model_class).filter(
                    self.model_class.id == entity_id
                ).first()
                
                if instance is None:
                    raise EntityNotFoundError(self.model_name, entity_id)
                
                if soft_delete and hasattr(instance, 'soft_delete'):
                    # 软删除
                    instance.soft_delete()
                    logger.debug(f"Soft deleted {self.model_name} with id: {entity_id}")
                else:
                    # 硬删除
                    session.delete(instance)
                    logger.debug(f"Hard deleted {self.model_name} with id: {entity_id}")
                
                session.flush()
                return True
                
        except EntityNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Error deleting {self.model_name} with id {entity_id}: {e}")
            raise DAOException(f"Failed to delete {self.model_name}", str(e))
    
    def get_all(self, include_deleted: bool = False) -> List[T]:
        """
        获取所有记录
        
        Args:
            include_deleted: 是否包含已删除的记录
            
        Returns:
            模型实例列表
        """
        try:
            with self.get_session() as session:
                query = session.query(self.model_class)
                
                # 过滤已删除的记录
                if not include_deleted and hasattr(self.model_class, 'is_deleted'):
                    query = query.filter(self.model_class.is_deleted == False)
                
                instances = query.all()
                logger.debug(f"Retrieved {len(instances)} {self.model_name} records")
                return instances
                
        except Exception as e:
            logger.error(f"Error getting all {self.model_name} records: {e}")
            raise DAOException(f"Failed to get all {self.model_name} records", str(e))
    
    def count(self, include_deleted: bool = False) -> int:
        """
        获取记录数量
        
        Args:
            include_deleted: 是否包含已删除的记录
            
        Returns:
            记录数量
        """
        try:
            with self.get_session() as session:
                query = session.query(self.model_class)
                
                # 过滤已删除的记录
                if not include_deleted and hasattr(self.model_class, 'is_deleted'):
                    query = query.filter(self.model_class.is_deleted == False)
                
                count = query.count()
                logger.debug(f"Counted {count} {self.model_name} records")
                return count
                
        except Exception as e:
            logger.error(f"Error counting {self.model_name} records: {e}")
            raise DAOException(f"Failed to count {self.model_name} records", str(e))
    
    def exists(self, entity_id: Any) -> bool:
        """
        检查记录是否存在
        
        Args:
            entity_id: 实体ID
            
        Returns:
            是否存在
        """
        return self.get_by_id(entity_id) is not None
    
    def query_builder(self, session: Optional[Session] = None) -> QueryBuilder:
        """
        获取查询构建器
        
        Args:
            session: 数据库会话，如果为None则使用新会话
            
        Returns:
            查询构建器实例
        """
        if session is None:
            session = get_session()
        return QueryBuilder(session, self.model_class)
    
    def batch_create(self, data_list: List[Dict[str, Any]]) -> List[T]:
        """
        批量创建记录
        
        Args:
            data_list: 数据字典列表
            
        Returns:
            创建的模型实例列表
        """
        try:
            with self.get_session() as session:
                instances = []
                
                for data in data_list:
                    # 验证数据
                    self._validate_create_data(data)
                    
                    # 创建实例
                    instance = self.model_class(**data)
                    instances.append(instance)
                
                # 批量保存
                session.add_all(instances)
                session.flush()
                
                # 刷新所有实例并分离
                for instance in instances:
                    session.refresh(instance)
                    self._ensure_attributes_loaded(session, instance)
                    session.expunge(instance)

                logger.debug(f"Batch created {len(instances)} {self.model_name} records")
                return instances
                
        except Exception as e:
            logger.error(f"Error batch creating {self.model_name} records: {e}")
            raise DAOException(f"Failed to batch create {self.model_name} records", str(e))
    
    def batch_update(self, updates: List[Dict[str, Any]]) -> List[T]:
        """
        批量更新记录
        
        Args:
            updates: 更新数据列表，每个字典必须包含'id'字段
            
        Returns:
            更新后的模型实例列表
        """
        try:
            with self.get_session() as session:
                instances = []
                
                for update_data in updates:
                    if 'id' not in update_data:
                        raise ValidationError('id', None, 'ID is required for batch update')
                    
                    entity_id = update_data.pop('id')
                    
                    # 获取实例
                    instance = session.query(self.model_class).filter(
                        self.model_class.id == entity_id
                    ).first()
                    
                    if instance is None:
                        raise EntityNotFoundError(self.model_name, entity_id)
                    
                    # 验证更新数据
                    self._validate_update_data(instance, update_data)
                    
                    # 更新字段
                    for key, value in update_data.items():
                        if hasattr(instance, key):
                            setattr(instance, key, value)
                    
                    instances.append(instance)
                
                session.flush()
                
                # 刷新所有实例并分离
                for instance in instances:
                    session.refresh(instance)
                    self._ensure_attributes_loaded(session, instance)
                    session.expunge(instance)

                logger.debug(f"Batch updated {len(instances)} {self.model_name} records")
                return instances
                
        except (EntityNotFoundError, ValidationError):
            raise
        except Exception as e:
            logger.error(f"Error batch updating {self.model_name} records: {e}")
            raise DAOException(f"Failed to batch update {self.model_name} records", str(e))
    
    def batch_delete(self, entity_ids: List[Any], soft_delete: bool = True) -> int:
        """
        批量删除记录
        
        Args:
            entity_ids: 实体ID列表
            soft_delete: 是否软删除
            
        Returns:
            删除的记录数量
        """
        try:
            with self.get_session() as session:
                query = session.query(self.model_class).filter(
                    self.model_class.id.in_(entity_ids)
                )
                
                instances = query.all()
                deleted_count = len(instances)
                
                if soft_delete and hasattr(self.model_class, 'is_deleted'):
                    # 软删除
                    for instance in instances:
                        if hasattr(instance, 'soft_delete'):
                            instance.soft_delete()
                        else:
                            instance.is_deleted = True
                else:
                    # 硬删除
                    for instance in instances:
                        session.delete(instance)
                
                session.flush()
                
                logger.debug(f"Batch deleted {deleted_count} {self.model_name} records")
                return deleted_count
                
        except Exception as e:
            logger.error(f"Error batch deleting {self.model_name} records: {e}")
            raise DAOException(f"Failed to batch delete {self.model_name} records", str(e))
    
    def _validate_create_data(self, data: Dict[str, Any]) -> None:
        """
        验证创建数据（子类可重写）
        
        Args:
            data: 创建数据
            
        Raises:
            ValidationError: 验证失败
        """
        pass
    
    def _validate_update_data(self, instance: T, data: Dict[str, Any]) -> None:
        """
        验证更新数据（子类可重写）
        
        Args:
            instance: 现有实例
            data: 更新数据
            
        Raises:
            ValidationError: 验证失败
        """
        pass

    def _ensure_attributes_loaded(self, session: Session, instance: T) -> None:
        """
        确保实例的所有属性都已加载，避免分离后的延迟加载问题

        Args:
            session: 数据库会话
            instance: 实例对象
        """
        try:
            # 获取实例的所有列属性
            if hasattr(instance.__class__, '__table__'):
                for column in instance.__class__.__table__.columns:
                    # 访问属性以触发加载
                    getattr(instance, column.name, None)
        except Exception as e:
            # 如果加载失败，记录警告但不中断流程
            logger.warning(f"Failed to preload attributes for {self.model_name}: {e}")
