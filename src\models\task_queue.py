"""
任务队列数据模型

定义任务队列相关的数据模型类。
"""

from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from sqlalchemy import String, Enum, Integer, ForeignKey, Index, DateTime, JSON
from sqlalchemy.orm import Mapped, mapped_column, relationship
import enum

from .base import BaseModel


class QueueStatus(enum.Enum):
    """队列状态枚举"""
    PENDING = "PENDING"      # 待处理
    PROCESSING = "PROCESSING" # 处理中
    COMPLETED = "COMPLETED"   # 已完成
    FAILED = "FAILED"        # 失败
    CANCELLED = "CANCELLED"   # 已取消
    TIMEOUT = "TIMEOUT"      # 超时


class QueuePriority(enum.Enum):
    """队列优先级枚举"""
    LOW = 1
    NORMAL = 5
    HIGH = 8
    URGENT = 10


class TaskQueue(BaseModel):
    """
    任务队列模型
    
    管理任务的执行队列和调度。
    """
    __tablename__ = "task_queue"
    
    # 关联任务
    task_id: Mapped[int] = mapped_column(
        ForeignKey("tasks.id"),
        nullable=False,
        comment="关联任务ID"
    )
    
    # 队列信息
    queue_name: Mapped[str] = mapped_column(
        String(100),
        default="default",
        nullable=False,
        comment="队列名称"
    )
    
    status: Mapped[QueueStatus] = mapped_column(
        Enum(QueueStatus),
        default=QueueStatus.PENDING,
        nullable=False,
        comment="队列状态"
    )
    
    priority: Mapped[int] = mapped_column(
        Integer,
        default=QueuePriority.NORMAL.value,
        nullable=False,
        comment="优先级（1-10，10最高）"
    )
    
    # 调度时间
    scheduled_time: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="计划执行时间"
    )
    
    started_time: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="开始处理时间"
    )
    
    completed_time: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="完成时间"
    )
    
    # 重试信息
    retry_count: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        comment="重试次数"
    )
    
    max_retries: Mapped[int] = mapped_column(
        Integer,
        default=3,
        nullable=False,
        comment="最大重试次数"
    )
    
    next_retry_time: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="下次重试时间"
    )
    
    # 执行信息
    worker_id: Mapped[Optional[str]] = mapped_column(
        String(100),
        nullable=True,
        comment="工作器ID"
    )
    
    execution_id: Mapped[Optional[str]] = mapped_column(
        String(100),
        nullable=True,
        comment="执行ID"
    )
    
    # 错误信息
    error_message: Mapped[Optional[str]] = mapped_column(
        String(1000),
        nullable=True,
        comment="错误消息"
    )
    
    # 额外数据
    extra_data: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        comment="额外数据"
    )
    
    # 关系定义
    task: Mapped["Task"] = relationship(
        "Task",
        back_populates="queue_items"
    )
    
    # 索引定义 - 优化队列查询和调度性能
    __table_args__ = (
        # 单字段索引
        Index('idx_task_queue_task_id', 'task_id'),
        Index('idx_task_queue_status', 'status'),
        Index('idx_task_queue_priority', 'priority'),
        Index('idx_task_queue_scheduled_time', 'scheduled_time'),
        Index('idx_task_queue_queue_name', 'queue_name'),
        Index('idx_task_queue_worker_id', 'worker_id'),
        Index('idx_task_queue_created_at', 'created_at'),
        Index('idx_task_queue_started_time', 'started_time'),
        Index('idx_task_queue_completed_time', 'completed_time'),
        Index('idx_task_queue_retry_count', 'retry_count'),

        # 复合索引 - 优化队列调度查询
        Index('idx_task_queue_status_priority', 'status', 'priority'),
        Index('idx_task_queue_status_scheduled_time', 'status', 'scheduled_time'),
        Index('idx_task_queue_queue_status', 'queue_name', 'status'),
        Index('idx_task_queue_worker_status', 'worker_id', 'status'),
        Index('idx_task_queue_task_status', 'task_id', 'status'),

        # 覆盖索引 - 队列调度优化
        Index('idx_task_queue_schedule_query', 'status', 'priority', 'scheduled_time', 'queue_name'),
        Index('idx_task_queue_worker_query', 'worker_id', 'status', 'started_time'),
        Index('idx_task_queue_performance_query', 'queue_name', 'status', 'priority', 'created_at'),
    )
    
    def __init__(self, **kwargs):
        """初始化任务队列实例"""
        super().__init__(**kwargs)
        if not self.extra_data:
            self.extra_data = {}
    
    def start_processing(self, worker_id: str, execution_id: str) -> None:
        """开始处理"""
        self.status = QueueStatus.PROCESSING
        self.worker_id = worker_id
        self.execution_id = execution_id
        self.started_time = datetime.now()
    
    def complete_processing(self, success: bool = True, 
                          error_message: Optional[str] = None) -> None:
        """完成处理"""
        self.completed_time = datetime.now()
        if success:
            self.status = QueueStatus.COMPLETED
        else:
            self.status = QueueStatus.FAILED
            self.error_message = error_message
    
    def cancel_processing(self) -> None:
        """取消处理"""
        self.status = QueueStatus.CANCELLED
        self.completed_time = datetime.now()
    
    def timeout_processing(self) -> None:
        """超时处理"""
        self.status = QueueStatus.TIMEOUT
        self.completed_time = datetime.now()
    
    def schedule_retry(self, retry_delay: int = 60) -> bool:
        """
        安排重试
        
        Args:
            retry_delay: 重试延迟（秒）
            
        Returns:
            是否可以重试
        """
        if self.retry_count >= self.max_retries:
            return False
        
        self.retry_count += 1
        self.status = QueueStatus.PENDING
        self.next_retry_time = datetime.now() + timedelta(seconds=retry_delay)
        self.worker_id = None
        self.execution_id = None
        return True
    
    @property
    def can_retry(self) -> bool:
        """是否可以重试"""
        return (self.status == QueueStatus.FAILED and 
                self.retry_count < self.max_retries)
    
    @property
    def is_ready_for_processing(self) -> bool:
        """是否准备好处理"""
        if self.status != QueueStatus.PENDING:
            return False
        
        now = datetime.now()
        
        # 检查计划时间
        if self.scheduled_time and self.scheduled_time > now:
            return False
        
        # 检查重试时间
        if self.next_retry_time and self.next_retry_time > now:
            return False
        
        return True
    
    @property
    def processing_duration(self) -> Optional[float]:
        """处理时长（秒）"""
        if not self.started_time:
            return None
        
        end_time = self.completed_time or datetime.now()
        return (end_time - self.started_time).total_seconds()
    
    def __repr__(self) -> str:
        """字符串表示"""
        return f"<TaskQueue(id={self.id}, task_id={self.task_id}, status={self.status.value})>"
    
    def __str__(self) -> str:
        """用户友好的字符串表示"""
        return f"TaskQueue: Task {self.task_id} ({self.status.value})"
