"""
脚本列表页面

提供脚本列表的查看、筛选、排序和管理功能。
基于T2.1主窗口框架、T2.2侧边导航组件、T2.3基础UI组件库和T2.4主题系统的高质量标准实现。
"""

from typing import Optional, Dict, Any, List
from datetime import datetime
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QFrame, QPushButton,
    QComboBox, QCheckBox, QMessageBox, QProgressBar
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread, pyqtSlot
from PyQt6.QtGui import QFont, QIcon

from src.utils.logger import get_logger
from src.ui.pages.base_page import BasePage
from src.ui.components.table import Table, ColumnConfig, SelectionMode
from src.ui.components.button import Button, ButtonType
from src.ui.components.input import Input, InputType
from src.ui.components.dialog import show_question_dialog, show_error_dialog, show_info_dialog
from src.ui.styles import get_theme_manager, ColorRole, ComponentSize
from src.services.service_factory import get_script_service
from src.models.script import Script, ScriptStatus, ScriptType


class ScriptDataLoader(QThread):
    """脚本数据加载线程"""
    
    # 信号定义
    data_loaded = pyqtSignal(list)  # 数据加载完成
    error_occurred = pyqtSignal(str)  # 错误发生
    
    def __init__(self, script_service, filters=None, search_text="", sort_column=None, sort_order=None):
        """
        初始化数据加载器
        
        Args:
            script_service: 脚本服务
            filters: 筛选条件
            search_text: 搜索文本
            sort_column: 排序列
            sort_order: 排序顺序
        """
        super().__init__()
        self.script_service = script_service
        self.filters = filters or {}
        self.search_text = search_text
        self.sort_column = sort_column
        self.sort_order = sort_order
    
    def run(self):
        """运行数据加载"""
        try:
            # 获取脚本列表
            scripts = self.script_service.get_all()
            
            # 应用筛选
            if self.filters:
                scripts = self._apply_filters(scripts)
            
            # 应用搜索
            if self.search_text:
                scripts = self._apply_search(scripts)
            
            # 转换为表格数据格式
            table_data = self._convert_to_table_data(scripts)
            
            self.data_loaded.emit(table_data)
            
        except Exception as e:
            self.error_occurred.emit(str(e))
    
    def _apply_filters(self, scripts: List[Script]) -> List[Script]:
        """应用筛选条件"""
        filtered_scripts = scripts
        
        # 状态筛选
        if 'status' in self.filters and self.filters['status']:
            status_filter = self.filters['status']
            if isinstance(status_filter, list):
                filtered_scripts = [s for s in filtered_scripts if s.status in status_filter]
            else:
                filtered_scripts = [s for s in filtered_scripts if s.status == status_filter]
        
        # 类型筛选
        if 'script_type' in self.filters and self.filters['script_type']:
            type_filter = self.filters['script_type']
            if isinstance(type_filter, list):
                filtered_scripts = [s for s in filtered_scripts if s.script_type in type_filter]
            else:
                filtered_scripts = [s for s in filtered_scripts if s.script_type == type_filter]
        
        return filtered_scripts
    
    def _apply_search(self, scripts: List[Script]) -> List[Script]:
        """应用搜索条件"""
        search_lower = self.search_text.lower()
        return [
            script for script in scripts
            if search_lower in script.name.lower() or 
               (script.description and search_lower in script.description.lower())
        ]
    
    def _convert_to_table_data(self, scripts: List[Script]) -> List[Dict[str, Any]]:
        """转换为表格数据格式"""
        table_data = []
        
        for script in scripts:
            # 格式化时间
            created_at = self._format_datetime(script.created_at)
            updated_at = self._format_datetime(script.updated_at)
            
            # 计算文件大小
            file_size = "未知"
            if script.file_path:
                try:
                    from pathlib import Path
                    path = Path(script.file_path)
                    if path.exists():
                        size_bytes = path.stat().st_size
                        file_size = self._format_file_size(size_bytes)
                except:
                    pass
            
            row_data = {
                'id': script.id,
                'name': script.name,
                'description': script.description or "",
                'status': script.status.value if script.status else "unknown",
                'script_type': script.script_type.value if script.script_type else "unknown",
                'file_size': file_size,
                'created_at': created_at,
                'updated_at': updated_at,
                'script_object': script  # 保存原始对象用于操作
            }
            table_data.append(row_data)
        
        return table_data
    
    def _format_datetime(self, dt: Optional[datetime]) -> str:
        """格式化日期时间"""
        if not dt:
            return "-"
        
        now = datetime.now()
        diff = now - dt
        
        if diff.days == 0:
            if diff.seconds < 3600:  # 1小时内
                minutes = diff.seconds // 60
                return f"{minutes}分钟前"
            else:  # 1小时到24小时
                hours = diff.seconds // 3600
                return f"{hours}小时前"
        elif diff.days == 1:
            return "昨天"
        elif diff.days < 7:
            return f"{diff.days}天前"
        else:
            return dt.strftime("%Y-%m-%d")
    
    def _format_file_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        else:
            return f"{size_bytes / (1024 * 1024):.1f} MB"


class ScriptListPage(BasePage):
    """
    脚本列表页面
    
    显示脚本列表，支持筛选、搜索、排序和管理操作。
    """
    
    # 信号定义
    script_selected = pyqtSignal(int)  # 脚本选择信号
    script_action_triggered = pyqtSignal(str, int)  # 脚本操作信号
    
    def __init__(self, parent: Optional[QWidget] = None):
        """
        初始化脚本列表页面
        
        Args:
            parent: 父窗口
        """
        # 初始化日志
        self.logger = get_logger(self.__class__.__name__)
        
        # 服务层
        self.script_service = get_script_service()
        
        # 主题管理器
        self.theme_manager = get_theme_manager()
        
        # UI组件
        self.table: Optional[Table] = None
        self.search_input: Optional[Input] = None
        self.status_filter: Optional[QComboBox] = None
        self.type_filter: Optional[QComboBox] = None
        
        # 数据加载器
        self.data_loader: Optional[ScriptDataLoader] = None
        
        # 当前筛选条件
        self.current_filters: Dict[str, Any] = {}
        self.current_search: str = ""
        
        # 调用父类初始化
        super().__init__(parent)
        
        # 加载初始数据
        self._load_data()
        
        self.logger.info("脚本列表页面初始化完成")
    
    def get_page_title(self) -> str:
        """获取页面标题"""
        return "脚本管理"
    
    def get_page_key(self) -> str:
        """获取页面键"""
        return "script_list"
    
    def _create_page_content(self, layout: QVBoxLayout) -> None:
        """创建页面内容"""
        # 创建工具栏
        self._create_toolbar(layout)
        
        # 创建筛选器
        self._create_filters(layout)
        
        # 创建表格
        self._create_table(layout)
    
    def _create_toolbar(self, layout: QVBoxLayout) -> None:
        """创建工具栏"""
        toolbar_frame = QFrame()
        toolbar_layout = QHBoxLayout(toolbar_frame)
        toolbar_layout.setContentsMargins(0, 0, 0, 0)
        
        # 页面标题
        title_label = QLabel("脚本管理")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        toolbar_layout.addWidget(title_label)
        
        toolbar_layout.addStretch()
        
        # 新建脚本按钮
        new_script_btn = Button("新建脚本", ButtonType.PRIMARY, ComponentSize.MEDIUM)
        new_script_btn.clicked.connect(self._on_new_script)
        toolbar_layout.addWidget(new_script_btn)
        
        # 刷新按钮
        refresh_btn = Button("刷新", ButtonType.SECONDARY, ComponentSize.MEDIUM)
        refresh_btn.clicked.connect(self._on_refresh)
        toolbar_layout.addWidget(refresh_btn)
        
        layout.addWidget(toolbar_frame)
    
    def _create_filters(self, layout: QVBoxLayout) -> None:
        """创建筛选器"""
        filters_frame = QFrame()
        filters_layout = QHBoxLayout(filters_frame)
        filters_layout.setContentsMargins(0, 8, 0, 8)
        
        # 搜索框
        self.search_input = Input("搜索脚本...", InputType.SEARCH, ComponentSize.MEDIUM)
        self.search_input.textChanged.connect(self._on_search_changed)
        filters_layout.addWidget(self.search_input)
        
        # 状态筛选
        status_label = QLabel("状态:")
        filters_layout.addWidget(status_label)
        
        self.status_filter = QComboBox()
        self.status_filter.addItem("全部", "")
        self.status_filter.addItem("活跃", ScriptStatus.ACTIVE.value)
        self.status_filter.addItem("禁用", ScriptStatus.DISABLED.value)
        self.status_filter.addItem("草稿", ScriptStatus.DRAFT.value)
        self.status_filter.currentTextChanged.connect(self._on_filter_changed)
        filters_layout.addWidget(self.status_filter)
        
        # 类型筛选
        type_label = QLabel("类型:")
        filters_layout.addWidget(type_label)
        
        self.type_filter = QComboBox()
        self.type_filter.addItem("全部", "")
        self.type_filter.addItem("Python", ScriptType.PYTHON.value)
        self.type_filter.addItem("Shell", ScriptType.SHELL.value)
        self.type_filter.addItem("Batch", ScriptType.BATCH.value)
        self.type_filter.currentTextChanged.connect(self._on_filter_changed)
        filters_layout.addWidget(self.type_filter)
        
        filters_layout.addStretch()
        
        layout.addWidget(filters_frame)
    
    def _create_table(self, layout: QVBoxLayout) -> None:
        """创建表格"""
        # 表格列配置
        columns = [
            ColumnConfig("name", "脚本名称", 200, True),
            ColumnConfig("description", "描述", 300, True),
            ColumnConfig("status", "状态", 80, True),
            ColumnConfig("script_type", "类型", 80, True),
            ColumnConfig("file_size", "大小", 80, True),
            ColumnConfig("created_at", "创建时间", 120, True),
            ColumnConfig("updated_at", "更新时间", 120, True),
        ]
        
        # 创建表格
        self.table = Table(
            columns=columns,
            selection_mode=SelectionMode.SINGLE,
            sortable=True,
            filterable=True
        )
        
        # 连接信号
        self.table.row_double_clicked.connect(self._on_row_double_clicked)
        self.table.context_menu_requested.connect(self._on_context_menu)
        
        layout.addWidget(self.table)
    
    def _load_data(self) -> None:
        """加载数据"""
        try:
            # 停止之前的加载器
            if self.data_loader and self.data_loader.isRunning():
                self.data_loader.quit()
                self.data_loader.wait()
            
            # 创建新的数据加载器
            self.data_loader = ScriptDataLoader(
                self.script_service,
                self.current_filters,
                self.current_search
            )
            
            # 连接信号
            self.data_loader.data_loaded.connect(self._on_data_loaded)
            self.data_loader.error_occurred.connect(self._on_data_error)
            
            # 启动加载
            self.data_loader.start()
            
        except Exception as e:
            self.logger.error(f"加载数据失败: {e}")
    
    @pyqtSlot(list)
    def _on_data_loaded(self, data: List[Dict[str, Any]]) -> None:
        """数据加载完成处理"""
        try:
            if self.table:
                self.table.set_data(data)
            
            self.logger.info(f"脚本列表数据加载完成，共{len(data)}条记录")
            
        except Exception as e:
            self.logger.error(f"处理加载数据失败: {e}")
    
    @pyqtSlot(str)
    def _on_data_error(self, error_msg: str) -> None:
        """数据加载错误处理"""
        self.logger.error(f"数据加载错误: {error_msg}")
        show_error_dialog(self, "加载失败", f"加载脚本数据失败：{error_msg}")
    
    def _on_search_changed(self, text: str) -> None:
        """搜索文本变化处理"""
        self.current_search = text
        # 延迟搜索，避免频繁查询
        QTimer.singleShot(500, self._load_data)
    
    def _on_filter_changed(self) -> None:
        """筛选条件变化处理"""
        # 更新筛选条件
        self.current_filters = {}
        
        if self.status_filter:
            status_value = self.status_filter.currentData()
            if status_value:
                self.current_filters['status'] = ScriptStatus(status_value)
        
        if self.type_filter:
            type_value = self.type_filter.currentData()
            if type_value:
                self.current_filters['script_type'] = ScriptType(type_value)
        
        # 重新加载数据
        self._load_data()
    
    def _on_row_double_clicked(self, row_data: Dict[str, Any]) -> None:
        """行双击处理"""
        script_id = row_data.get('id')
        if script_id:
            self.script_selected.emit(script_id)
            self.logger.info(f"脚本双击: {script_id}")
    
    def _on_context_menu(self, row_data: Dict[str, Any], position) -> None:
        """右键菜单处理"""
        # TODO: 实现右键菜单
        pass
    
    def _on_new_script(self) -> None:
        """新建脚本处理"""
        self.script_action_triggered.emit("new", 0)
        self.logger.info("新建脚本操作")
    
    def _on_refresh(self) -> None:
        """刷新处理"""
        self._load_data()
        self.logger.info("脚本列表已刷新")
    
    def refresh_page(self) -> None:
        """刷新页面"""
        self._load_data()
        self.logger.info("脚本列表页面已刷新")
