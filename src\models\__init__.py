"""
数据模型包

导出所有数据模型类和相关功能。
"""

from .base import Base, BaseModel, TimestampMixin, SoftDeleteMixin, AuditMixin

# 核心模型
from .script import Script, ScriptType, ScriptStatus
from .task import Task, TaskStatus, TaskPriority, ScheduleType
from .execution import Execution, ExecutionStatus
from .config import Config, ConfigType, ConfigScope

# 扩展模型
from .script_version import ScriptVersion
from .task_queue import TaskQueue, QueueStatus, QueuePriority
from .system_log import SystemLog, LogLevel
from .notification_history import NotificationHistory, NotificationType, NotificationStatus, NotificationPriority

# 导出所有模型类
__all__ = [
    # 基础类
    'Base',
    'BaseModel',
    'TimestampMixin',
    'SoftDeleteMixin',
    'AuditMixin',

    # 核心模型
    'Script',
    'ScriptType',
    'ScriptStatus',
    'Task',
    'TaskStatus',
    'TaskPriority',
    'ScheduleType',
    'Execution',
    'ExecutionStatus',
    'Config',
    'ConfigType',
    'ConfigScope',

    # 扩展模型
    'ScriptVersion',
    'TaskQueue',
    'QueueStatus',
    'QueuePriority',
    'SystemLog',
    'LogLevel',
    'NotificationHistory',
    'NotificationType',
    'NotificationStatus',
    'NotificationPriority',
]
