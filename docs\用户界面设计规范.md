# 自动化任务管理工具用户界面设计规范

## 📋 文档概述

### 文档目的
本文档详细定义了自动化任务管理工具的用户界面设计规范，包括设计原则、视觉规范、组件标准、交互规范和布局设计，确保整个应用程序具有一致、直观、高效的用户体验。

### 设计目标
- **一致性**: 统一的视觉语言和交互模式
- **易用性**: 直观的操作流程和友好的用户体验
- **效率性**: 快速完成任务的界面设计
- **美观性**: 现代化的视觉设计和良好的视觉层次

### 目标用户
- **主要用户**: 具备基本计算机操作能力的个人用户
- **使用环境**: Windows桌面环境
- **使用场景**: 日常工作中的自动化任务管理

## 📑 目录

1. [设计原则](#设计原则)
2. [视觉设计系统](#视觉设计系统)
3. [组件设计规范](#组件设计规范)
4. [交互设计规范](#交互设计规范)
5. [布局设计规范](#布局设计规范)
6. [响应式设计](#响应式设计)
7. [无障碍设计](#无障碍设计)
8. [主题系统](#主题系统)

## 🎯 设计原则

### 1.1 核心设计理念

#### 一致性原则 (Consistency)
- **视觉一致性**: 统一的颜色方案、字体规范、图标风格和组件样式
- **交互一致性**: 相同功能在不同页面保持相同的操作方式
- **信息架构一致性**: 统一的导航结构和信息层次
- **术语一致性**: 统一的功能命名和文案表达

#### 简洁性原则 (Simplicity)
- **界面简洁**: 避免不必要的装饰元素，突出核心功能
- **信息层次清晰**: 通过视觉层次引导用户关注重点
- **操作流程简化**: 减少用户完成任务所需的步骤
- **认知负担最小**: 降低用户理解和使用的复杂度

#### 可用性原则 (Usability)
- **响应式设计**: 适应不同屏幕尺寸和分辨率
- **无障碍设计**: 支持键盘导航和屏幕阅读器
- **错误预防**: 通过设计减少用户操作错误
- **反馈及时**: 为用户操作提供即时的视觉反馈

#### 效率原则 (Efficiency)
- **快速访问**: 常用功能易于发现和访问
- **批量操作**: 支持多项目同时操作
- **快捷键支持**: 为高频操作提供键盘快捷键
- **智能默认**: 提供合理的默认值和建议

### 1.2 界面设计目标

#### 用户体验目标
- **学习成本低**: 新用户能够快速上手
- **操作效率高**: 熟练用户能够高效完成任务
- **错误率低**: 减少用户操作错误
- **满意度高**: 提供愉悦的使用体验

#### 技术实现目标
- **性能优秀**: 界面响应迅速，动画流畅
- **兼容性好**: 支持不同分辨率和DPI设置
- **可维护性强**: 组件化设计，便于维护和扩展
- **可访问性好**: 支持无障碍访问

## 🎨 视觉设计系统

### 2.1 颜色系统

#### 主色调定义
```css
/* 主色调 - 蓝色系 */
--primary-color: #1976d2;           /* 主要蓝色 */
--primary-light: #42a5f5;           /* 浅蓝色 */
--primary-dark: #1565c0;            /* 深蓝色 */
--primary-50: rgba(25, 118, 210, 0.05);   /* 5% 透明度 */
--primary-100: rgba(25, 118, 210, 0.1);   /* 10% 透明度 */
--primary-200: rgba(25, 118, 210, 0.2);   /* 20% 透明度 */
```

#### 中性色系
```css
/* 背景色 */
--bg-primary: #ffffff;              /* 主背景 - 纯白 */
--bg-secondary: #f5f5f5;            /* 次背景 - 浅灰 */
--bg-tertiary: #fafafa;             /* 三级背景 */
--bg-hover: #f0f0f0;                /* 悬停背景 */
--bg-selected: #e3f2fd;             /* 选中背景 */
--bg-disabled: #f5f5f5;             /* 禁用背景 */

/* 文字颜色 */
--text-primary: #333333;            /* 主文字 - 深灰 */
--text-secondary: #666666;          /* 次文字 - 中灰 */
--text-tertiary: #999999;           /* 弱化文字 - 浅灰 */
--text-disabled: #cccccc;           /* 禁用文字 */
--text-inverse: #ffffff;            /* 反色文字 - 白色 */

/* 边框颜色 */
--border-primary: #e0e0e0;          /* 主边框 */
--border-secondary: #f0f0f0;        /* 浅边框 */
--border-focus: #1976d2;            /* 焦点边框 */
--border-error: #f44336;            /* 错误边框 */
```

#### 状态色系
```css
/* 成功状态 - 绿色系 */
--success-color: #4caf50;           /* 成功主色 */
--success-light: #81c784;           /* 成功浅色 */
--success-dark: #388e3c;            /* 成功深色 */
--success-bg: #e8f5e8;              /* 成功背景 */

/* 警告状态 - 橙色系 */
--warning-color: #ff9800;           /* 警告主色 */
--warning-light: #ffb74d;           /* 警告浅色 */
--warning-dark: #f57c00;            /* 警告深色 */
--warning-bg: #fff3e0;              /* 警告背景 */

/* 错误状态 - 红色系 */
--error-color: #f44336;             /* 错误主色 */
--error-light: #e57373;             /* 错误浅色 */
--error-dark: #d32f2f;              /* 错误深色 */
--error-bg: #ffebee;                /* 错误背景 */

/* 信息状态 - 蓝色系 */
--info-color: #2196f3;              /* 信息主色 */
--info-light: #64b5f6;              /* 信息浅色 */
--info-dark: #1976d2;               /* 信息深色 */
--info-bg: #e3f2fd;                 /* 信息背景 */
```

#### 颜色使用规范
- **主色调使用**: 用于主要操作按钮、链接、选中状态
- **状态色使用**: 严格按照语义使用，不可混用
- **对比度要求**: 文字与背景对比度不低于4.5:1
- **色彩无障碍**: 不依赖颜色作为唯一的信息传达方式

### 2.2 字体系统

#### 字体族定义
```css
/* 主要字体 - 无衬线字体 */
--font-family-primary: "Microsoft YaHei", "PingFang SC", "Helvetica Neue", Arial, sans-serif;

/* 等宽字体 - 代码显示 */
--font-family-mono: "Consolas", "Monaco", "Courier New", monospace;

/* 数字字体 - 数据显示 */
--font-family-numeric: "Roboto", "Helvetica Neue", Arial, sans-serif;
```

#### 字体大小规范
```css
/* 字体大小层级 */
--font-size-xs: 10px;               /* 极小字体 - 辅助信息 */
--font-size-sm: 12px;               /* 小字体 - 次要信息 */
--font-size-base: 14px;             /* 基础字体 - 正文内容 */
--font-size-lg: 16px;               /* 大字体 - 重要信息 */
--font-size-xl: 18px;               /* 超大字体 - 小标题 */
--font-size-2xl: 20px;              /* 二级标题 */
--font-size-3xl: 24px;              /* 一级标题 */
--font-size-4xl: 28px;              /* 主标题 */

/* 行高规范 */
--line-height-tight: 1.2;           /* 紧凑行高 - 标题 */
--line-height-normal: 1.5;          /* 正常行高 - 正文 */
--line-height-loose: 1.8;           /* 宽松行高 - 长文本 */
```

#### 字体权重规范
```css
--font-weight-light: 300;           /* 细体 */
--font-weight-normal: 400;          /* 正常 */
--font-weight-medium: 500;          /* 中等 */
--font-weight-semibold: 600;        /* 半粗体 */
--font-weight-bold: 700;            /* 粗体 */
```

### 2.3 间距系统

#### 间距规范定义
```css
/* 基础间距单位 */
--spacing-unit: 8px;                /* 基础间距单位 */

/* 间距层级 */
--spacing-xs: 4px;                  /* 极小间距 */
--spacing-sm: 8px;                  /* 小间距 */
--spacing-md: 16px;                 /* 中等间距 */
--spacing-lg: 24px;                 /* 大间距 */
--spacing-xl: 32px;                 /* 超大间距 */
--spacing-2xl: 48px;                /* 二倍大间距 */
--spacing-3xl: 64px;                /* 三倍大间距 */

/* 组件内部间距 */
--padding-xs: 4px 8px;              /* 极小内边距 */
--padding-sm: 8px 12px;             /* 小内边距 */
--padding-md: 12px 16px;            /* 中等内边距 */
--padding-lg: 16px 24px;            /* 大内边距 */
--padding-xl: 20px 32px;            /* 超大内边距 */
```

#### 间距使用原则
- **8px基础单位**: 所有间距都是8px的倍数
- **垂直韵律**: 保持垂直方向的间距一致性
- **组件间距**: 相关组件间距小，不相关组件间距大
- **呼吸感**: 适当的留白提升视觉舒适度

### 2.4 阴影系统

#### 阴影层级定义
```css
/* 阴影层级 */
--shadow-none: none;                /* 无阴影 */
--shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);           /* 微阴影 */
--shadow-md: 0 2px 4px rgba(0, 0, 0, 0.1);            /* 小阴影 */
--shadow-lg: 0 4px 8px rgba(0, 0, 0, 0.15);           /* 中阴影 */
--shadow-xl: 0 8px 16px rgba(0, 0, 0, 0.2);           /* 大阴影 */
--shadow-2xl: 0 16px 32px rgba(0, 0, 0, 0.25);        /* 超大阴影 */

/* 特殊阴影 */
--shadow-focus: 0 0 0 3px rgba(25, 118, 210, 0.2);    /* 焦点阴影 */
--shadow-error: 0 0 0 3px rgba(244, 67, 54, 0.2);     /* 错误阴影 */
--shadow-inset: inset 0 1px 2px rgba(0, 0, 0, 0.1);   /* 内阴影 */
```

#### 阴影使用场景
- **卡片组件**: 使用中等阴影营造层次感
- **悬浮元素**: 使用大阴影表示悬浮状态
- **焦点状态**: 使用焦点阴影表示键盘焦点
- **按钮按下**: 使用内阴影表示按下状态

### 2.5 圆角系统

#### 圆角规范定义
```css
/* 圆角层级 */
--radius-none: 0;                   /* 无圆角 */
--radius-sm: 2px;                   /* 小圆角 */
--radius-md: 4px;                   /* 中等圆角 */
--radius-lg: 8px;                   /* 大圆角 */
--radius-xl: 12px;                  /* 超大圆角 */
--radius-full: 50%;                 /* 完全圆角 */
```

#### 圆角使用规范
- **按钮组件**: 使用中等圆角，保持现代感
- **输入框**: 使用小圆角，保持简洁
- **卡片容器**: 使用大圆角，增加亲和力
- **头像图片**: 使用完全圆角

## 🧩 组件设计规范

### 3.1 按钮组件规范

#### 按钮尺寸规范
```css
/* 按钮尺寸 */
.btn-xs {
    min-height: 24px;
    padding: 2px 8px;
    font-size: 12px;
}

.btn-sm {
    min-height: 32px;
    padding: 6px 12px;
    font-size: 14px;
}

.btn-md {
    min-height: 40px;
    padding: 10px 16px;
    font-size: 14px;
}

.btn-lg {
    min-height: 48px;
    padding: 14px 24px;
    font-size: 16px;
}
```

#### 按钮类型规范
- **主要按钮**: 蓝色背景，白色文字，用于主要操作
- **次要按钮**: 白色背景，蓝色边框和文字，用于次要操作
- **危险按钮**: 红色背景，白色文字，用于删除等危险操作
- **文字按钮**: 无背景，蓝色文字，用于辅助操作

#### 按钮状态规范
- **正常状态**: 默认样式
- **悬停状态**: 背景色加深10%，添加微阴影
- **按下状态**: 背景色加深20%，添加内阴影
- **禁用状态**: 灰色背景，灰色文字，移除交互

#### 按钮间距规范
- **水平间距**: 相邻按钮间距8px
- **垂直间距**: 上下按钮间距16px
- **按钮组**: 按钮组内按钮无间距，整体圆角

### 3.2 输入组件规范

#### 输入框规范
```css
.input {
    height: 36px;
    padding: 8px 12px;
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    font-size: 14px;
    line-height: 1.5;
    transition: all 0.2s ease;
}

.input:focus {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-focus);
    outline: none;
}

.input:disabled {
    background-color: var(--bg-disabled);
    color: var(--text-disabled);
    cursor: not-allowed;
}

.input.error {
    border-color: var(--error-color);
    box-shadow: var(--shadow-error);
}
```

#### 下拉选择框规范
- **最小高度**: 36px
- **选项高度**: 32px
- **最大显示**: 8个选项，超出显示滚动条
- **搜索功能**: 选项超过10个时提供搜索

#### 复选框和单选框规范
- **尺寸**: 16x16px
- **间距**: 文字与框间距8px
- **对齐**: 与文字基线对齐
- **状态**: 支持选中、未选中、禁用、部分选中

### 3.3 表格组件规范

#### 表格基础规范
```css
.table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--bg-primary);
}

/* 隐藏默认垂直表头 */
.table thead {
    border: none;
}

.table th {
    height: 40px;
    padding: 12px 16px;
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
    font-weight: var(--font-weight-medium);
    text-align: center;
}

.table td {
    height: 40px;
    padding: 8px 16px;
    border-bottom: 1px solid var(--border-secondary);
    vertical-align: middle;
    text-align: center;
}

/* 序号列特殊样式 */
.table th.col-index,
.table td.col-index {
    width: 28px;
    min-width: 28px;
    max-width: 28px;
    padding: 8px 4px;
    text-align: center;
    font-size: 12px;
    color: var(--text-tertiary);
    background-color: var(--bg-tertiary);
}

.table tr:hover {
    background-color: var(--bg-hover);
}

.table tr.selected {
    background-color: var(--bg-selected);
}
```

#### 表格功能规范
- **序号列**: 第一列为序号列，宽度32px，显示行号
- **排序功能**: 点击表头排序，显示排序图标
- **筛选功能**: 表头提供筛选下拉框
- **分页功能**: 表格底部显示分页控件
- **行选择**: 支持单选和多选
- **行操作**: 每行末尾提供操作按钮
- **内容对齐**: 所有内容默认居中显示
- **隐藏垂直行列**：隐藏数据表的垂直表头

### 3.4 导航组件规范

#### 顶部导航规范
```css
.navbar {
    height: 56px;
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    padding: 0 24px;
}

.navbar-brand {
    font-size: 18px;
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
}

.navbar-nav {
    display: flex;
    margin-left: auto;
}

.navbar-item {
    padding: 8px 16px;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
}

.navbar-item:hover {
    background-color: var(--bg-hover);
    color: var(--text-primary);
}

.navbar-item.active {
    background-color: var(--primary-color);
    color: var(--text-inverse);
}
```

#### 侧边导航规范
- **宽度**: 默认200px，可收缩至64px
- **层级**: 最多3级导航层级
- **图标**: 每个导航项配备16x16px图标
- **状态**: 支持展开、收缩、选中状态

#### 面包屑导航规范
- **分隔符**: 使用"/"或">"分隔
- **层级**: 最多显示5级，超出使用省略号
- **交互**: 除当前页外，其他层级可点击跳转

## 🖱️ 交互设计规范

### 4.1 交互状态规范

#### 悬停状态 (Hover)
- **触发条件**: 鼠标悬停在可交互元素上
- **视觉反馈**: 背景色变化、阴影增强、颜色加深
- **动画时长**: 0.2秒缓动过渡
- **适用元素**: 按钮、链接、卡片、菜单项

#### 焦点状态 (Focus)
- **触发条件**: 键盘Tab键聚焦或点击聚焦
- **视觉反馈**: 显示焦点轮廓，通常为蓝色边框
- **无障碍**: 确保焦点轮廓清晰可见
- **适用元素**: 所有可交互元素

#### 激活状态 (Active)
- **触发条件**: 鼠标按下或键盘按键按下
- **视觉反馈**: 背景色进一步加深，可能添加内阴影
- **动画时长**: 即时响应，无过渡动画
- **适用元素**: 按钮、链接等可点击元素

#### 禁用状态 (Disabled)
- **视觉表现**: 降低透明度至50%，使用灰色调
- **交互行为**: 移除所有交互能力，鼠标样式为not-allowed
- **无障碍**: 添加aria-disabled属性
- **适用场景**: 条件不满足时的表单元素和按钮

### 4.2 动画规范

#### 动画时长规范
```css
/* 动画时长 */
--duration-fast: 0.1s;              /* 快速动画 - 按钮反馈 */
--duration-normal: 0.2s;            /* 正常动画 - 悬停效果 */
--duration-slow: 0.3s;              /* 慢速动画 - 页面切换 */
--duration-slower: 0.5s;            /* 更慢动画 - 复杂转换 */

/* 缓动函数 */
--easing-linear: linear;            /* 线性 */
--easing-ease: ease;                /* 标准缓动 */
--easing-ease-in: ease-in;          /* 缓入 */
--easing-ease-out: ease-out;        /* 缓出 */
--easing-ease-in-out: ease-in-out;  /* 缓入缓出 */
```

#### 动画类型规范
- **淡入淡出**: 用于模态框、提示信息的显示隐藏
- **滑动**: 用于抽屉、下拉菜单的展开收起
- **缩放**: 用于按钮点击、图片预览的放大缩小
- **旋转**: 用于加载指示器、刷新按钮

#### 动画性能规范
- **硬件加速**: 使用transform和opacity属性
- **避免重排**: 不使用会引起重排的属性
- **帧率要求**: 保持60fps流畅度
- **降级处理**: 为低性能设备提供简化动画

### 4.3 反馈机制规范

#### 加载状态反馈
- **按钮加载**: 显示旋转图标，禁用按钮交互
- **页面加载**: 显示骨架屏或进度条
- **数据加载**: 显示加载指示器和加载文案
- **超时处理**: 超过5秒显示重试选项

#### 操作结果反馈
- **成功反馈**: 绿色提示信息，自动消失
- **错误反馈**: 红色提示信息，需要用户手动关闭
- **警告反馈**: 橙色提示信息，提醒用户注意
- **信息反馈**: 蓝色提示信息，提供额外信息

#### 表单验证反馈
- **实时验证**: 失去焦点时进行验证
- **错误提示**: 在字段下方显示错误信息
- **成功提示**: 显示绿色对勾图标
- **整体验证**: 提交时进行整体验证并定位错误

## 📐 布局设计规范

### 5.1 整体布局架构

#### 主窗口布局结构
```
┌─────────────────────────────────────────────────────────────┐
│                    菜单栏 (MenuBar) - 24px                  │
├─────────────────────────────────────────────────────────────┤
│                    工具栏 (ToolBar) - 48px                  │
├─────────────┬───────────────────────────────────────────────┤
│             │                                               │
│  导航树     │              主内容区域                       │
│ (200px)     │           (TabContainer)                      │
│             │                                               │
│             │                                               │
│             │                                               │
├─────────────┴───────────────────────────────────────────────┤
│                    状态栏 (StatusBar) - 38px                │
└─────────────────────────────────────────────────────────────┘
```

#### 窗口基本属性规范
```css
/* 主窗口规范 */
.main-window {
    min-width: 1024px;              /* 最小宽度 */
    min-height: 768px;              /* 最小高度 */
    width: 1200px;                  /* 默认宽度 */
    height: 800px;                  /* 默认高度 */
    background-color: var(--bg-secondary);
}

/* 布局比例 */
.sidebar {
    width: 200px;                   /* 侧边栏宽度 */
    min-width: 100px;               /* 最小宽度 */
    max-width: 300px;               /* 最大宽度 */
    background-color: var(--bg-primary);
    border-right: 1px solid var(--border-primary);
}

.main-content {
    flex: 1;                        /* 自适应剩余空间 */
    background-color: var(--bg-primary);
    overflow: hidden;
}
```

### 5.2 页面内容布局

#### 页面容器规范
```css
.page-container {
    height: 100%;
    padding: 24px;
    overflow-y: auto;
}

.page-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--border-secondary);
}

.page-title {
    font-size: 24px;
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: 8px;
}

.page-description {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: var(--line-height-normal);
}

.page-content {
    flex: 1;
}
```

#### 卡片布局规范
```css
.card {
    background: var(--bg-primary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.card-header {
    padding: 16px 20px;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
}

.card-title {
    font-size: 16px;
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
}

.card-content {
    padding: 20px;
}

.card-footer {
    padding: 12px 20px;
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-primary);
}
```

## 📱 响应式设计

### 6.1 断点系统

#### 断点定义
```css
/* 响应式断点 */
--breakpoint-xs: 480px;             /* 超小屏幕 */
--breakpoint-sm: 768px;             /* 小屏幕 */
--breakpoint-md: 1024px;            /* 中等屏幕 */
--breakpoint-lg: 1280px;            /* 大屏幕 */
--breakpoint-xl: 1920px;            /* 超大屏幕 */
```

#### 布局适配规范
- **超小屏幕**: 单列布局，隐藏次要信息
- **小屏幕**: 两列布局，简化导航
- **中等屏幕**: 三列布局，完整功能
- **大屏幕**: 多列布局，充分利用空间
- **超大屏幕**: 限制最大宽度，居中显示

### 6.2 组件响应式规范

#### 导航响应式
```css
/* 大屏幕 (>= 1280px) */
@media (min-width: 1280px) {
    .sidebar {
        width: 280px;
    }
}

/* 中等屏幕 (768px - 1279px) */
@media (min-width: 768px) and (max-width: 1279px) {
    .sidebar {
        width: 200px;
    }
}

/* 小屏幕 (< 768px) */
@media (max-width: 767px) {
    .sidebar {
        position: absolute;
        left: -240px;
        z-index: 1000;
        transition: left 0.3s ease-out;
    }

    .sidebar.open {
        left: 0;
    }
}
```

## ♿ 无障碍设计

### 7.1 键盘导航规范

#### Tab顺序规范
- **逻辑顺序**: Tab键导航顺序符合视觉和逻辑顺序
- **焦点可见**: 所有焦点状态清晰可见
- **跳过链接**: 提供跳过导航的链接
- **焦点陷阱**: 模态框内的焦点循环

#### 快捷键支持
```css
/* 快捷键定义 */
Ctrl+N: 新建任务
Ctrl+S: 保存
Ctrl+F: 搜索
F5: 刷新
Esc: 关闭对话框
```

### 7.2 屏幕阅读器支持

#### ARIA属性规范
```html
<!-- 按钮示例 -->
<button aria-label="删除任务" aria-describedby="delete-help">
    <icon>🗑️</icon>
</button>
<div id="delete-help" class="sr-only">
    此操作将永久删除任务，无法恢复
</div>

<!-- 表单示例 -->
<label for="task-name">任务名称</label>
<input id="task-name" aria-required="true" aria-invalid="false">
<div id="name-error" aria-live="polite"></div>
```

#### 语义化标签
- **标题结构**: 使用正确的h1-h6标题层级
- **列表结构**: 使用ul/ol和li标签
- **表格结构**: 使用table、thead、tbody、th、td
- **表单结构**: 使用form、fieldset、legend、label

### 7.3 色彩无障碍

#### 对比度要求
- **正常文字**: 对比度不低于4.5:1
- **大文字**: 对比度不低于3:1
- **图标**: 对比度不低于3:1
- **状态指示**: 不依赖颜色作为唯一标识

#### 色盲友好设计
- **状态指示**: 使用图标+颜色+文字
- **图表设计**: 使用图案+颜色区分
- **链接识别**: 使用下划线+颜色
- **错误提示**: 使用图标+颜色+文字

## 🎨 主题系统

### 8.1 主题切换机制

#### 主题变量定义
```css
/* 浅色主题 (默认) */
:root {
    --theme-name: 'light';
    /* 颜色变量已在前面定义 */
}

/* 深色主题 */
[data-theme='dark'] {
    --theme-name: 'dark';

    /* 重新定义颜色变量 */
    --bg-primary: #1e1e1e;
    --bg-secondary: #252526;
    --bg-tertiary: #2d2d30;
    --bg-hover: #2a2d2e;
    --bg-selected: #094771;

    --text-primary: #cccccc;
    --text-secondary: #969696;
    --text-tertiary: #6a6a6a;

    --border-primary: #3e3e42;
    --border-secondary: #2d2d30;
}
```

#### 主题切换实现
```javascript
// 主题切换函数
function switchTheme(themeName) {
    document.documentElement.setAttribute('data-theme', themeName);
    localStorage.setItem('theme', themeName);

    // 触发主题变更事件
    window.dispatchEvent(new CustomEvent('themeChanged', {
        detail: { theme: themeName }
    }));
}

// 主题初始化
function initTheme() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    switchTheme(savedTheme);
}
```

### 8.2 图标系统规范

#### 图标库规范
- **图标库**: 使用Material Design Icons或Feather Icons
- **图标格式**: SVG格式，支持颜色和尺寸自定义
- **图标尺寸**: 16px、20px、24px、32px四个标准尺寸
- **图标颜色**: 继承父元素文字颜色

#### 图标使用规范
```css
.icon {
    display: inline-block;
    width: 1em;
    height: 1em;
    fill: currentColor;
    vertical-align: middle;
}

.icon-sm { font-size: 16px; }
.icon-md { font-size: 20px; }
.icon-lg { font-size: 24px; }
.icon-xl { font-size: 32px; }
```

#### 常用图标定义
- **操作图标**: 新建(+)、编辑(✏️)、删除(🗑️)、保存(💾)
- **状态图标**: 成功(✓)、失败(✗)、警告(⚠️)、信息(ℹ️)
- **导航图标**: 首页(🏠)、设置(⚙️)、帮助(❓)、返回(←)
- **功能图标**: 搜索(🔍)、筛选(🔽)、排序(↕️)、刷新(🔄)

---

**文档版本**: 1.0
**最后更新**: 2024-12-19
**文档状态**: 已完成
