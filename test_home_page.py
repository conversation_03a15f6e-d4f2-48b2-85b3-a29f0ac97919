#!/usr/bin/env python3
"""
首页测试脚本

测试改进后的首页数据集成功能。
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt6.QtCore import Qt

from src.ui.pages.home_page import HomePage
from src.utils.logger import setup_logging

def main():
    """主函数"""
    # 设置日志
    setup_logging()
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = QMainWindow()
    main_window.setWindowTitle("首页测试")
    main_window.setGeometry(100, 100, 1200, 800)
    
    # 创建首页
    home_page = HomePage()
    
    # 设置中央窗口
    main_window.setCentralWidget(home_page)
    
    # 显示窗口
    main_window.show()
    
    # 运行应用程序
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
