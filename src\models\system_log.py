"""
系统日志数据模型

定义系统日志相关的数据模型类。
"""

from typing import Optional, Dict, Any
from sqlalchemy import String, Text, Enum, Integer, ForeignKey, Index, JSON
from sqlalchemy.orm import Mapped, mapped_column, relationship
import enum

from .base import BaseModel


class LogLevel(enum.Enum):
    """日志级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class SystemLog(BaseModel):
    """
    系统日志模型
    
    存储系统运行过程中的日志信息。
    """
    __tablename__ = "system_logs"
    
    # 日志基本信息
    level: Mapped[LogLevel] = mapped_column(
        Enum(LogLevel),
        nullable=False,
        comment="日志级别"
    )
    
    module: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        comment="模块名称"
    )
    
    function: Mapped[Optional[str]] = mapped_column(
        String(100),
        nullable=True,
        comment="函数名称"
    )
    
    line_number: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        comment="行号"
    )
    
    # 日志内容
    message: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        comment="日志消息"
    )
    
    details: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="详细信息"
    )
    
    # 异常信息
    exception_type: Mapped[Optional[str]] = mapped_column(
        String(200),
        nullable=True,
        comment="异常类型"
    )
    
    exception_message: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="异常消息"
    )
    
    traceback: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="异常堆栈"
    )
    
    # 关联信息
    related_task_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("tasks.id"),
        nullable=True,
        comment="相关任务ID"
    )
    
    related_script_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("scripts.id"),
        nullable=True,
        comment="相关脚本ID"
    )
    
    related_execution_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("executions.id"),
        nullable=True,
        comment="相关执行ID"
    )
    
    # 环境信息
    user_agent: Mapped[Optional[str]] = mapped_column(
        String(500),
        nullable=True,
        comment="用户代理"
    )
    
    ip_address: Mapped[Optional[str]] = mapped_column(
        String(50),
        nullable=True,
        comment="IP地址"
    )
    
    session_id: Mapped[Optional[str]] = mapped_column(
        String(100),
        nullable=True,
        comment="会话ID"
    )
    
    # 上下文信息
    context: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        comment="上下文信息"
    )
    
    # 关系定义
    related_task: Mapped[Optional["Task"]] = relationship(
        "Task",
        foreign_keys=[related_task_id]
    )
    
    related_script: Mapped[Optional["Script"]] = relationship(
        "Script",
        foreign_keys=[related_script_id]
    )
    
    related_execution: Mapped[Optional["Execution"]] = relationship(
        "Execution",
        foreign_keys=[related_execution_id]
    )
    
    # 索引定义
    __table_args__ = (
        Index('idx_system_log_level', 'level'),
        Index('idx_system_log_module', 'module'),
        Index('idx_system_log_created_at', 'created_at'),
        Index('idx_system_log_related_task_id', 'related_task_id'),
        Index('idx_system_log_related_script_id', 'related_script_id'),
        Index('idx_system_log_related_execution_id', 'related_execution_id'),
    )
    
    def __init__(self, **kwargs):
        """初始化系统日志实例"""
        super().__init__(**kwargs)
        if not self.context:
            self.context = {}
    
    @classmethod
    def create_log(cls, level: LogLevel, module: str, message: str,
                   function: Optional[str] = None,
                   line_number: Optional[int] = None,
                   details: Optional[str] = None,
                   exception: Optional[Exception] = None,
                   context: Optional[Dict[str, Any]] = None,
                   **kwargs) -> 'SystemLog':
        """
        创建日志记录
        
        Args:
            level: 日志级别
            module: 模块名称
            message: 日志消息
            function: 函数名称
            line_number: 行号
            details: 详细信息
            exception: 异常对象
            context: 上下文信息
            **kwargs: 其他参数
            
        Returns:
            系统日志实例
        """
        log_data = {
            'level': level,
            'module': module,
            'message': message,
            'function': function,
            'line_number': line_number,
            'details': details,
            'context': context or {},
            **kwargs
        }
        
        if exception:
            log_data.update({
                'exception_type': type(exception).__name__,
                'exception_message': str(exception),
                'traceback': cls._get_traceback_string(exception)
            })
        
        return cls(**log_data)
    
    @staticmethod
    def _get_traceback_string(exception: Exception) -> str:
        """获取异常堆栈字符串"""
        import traceback
        return ''.join(traceback.format_exception(
            type(exception), exception, exception.__traceback__
        ))
    
    def add_context(self, key: str, value: Any) -> None:
        """添加上下文信息"""
        if not self.context:
            self.context = {}
        self.context[key] = value
    
    def get_formatted_message(self) -> str:
        """获取格式化的日志消息"""
        parts = [f"[{self.level.value}]"]
        
        if self.module:
            parts.append(f"{self.module}")
        
        if self.function:
            parts.append(f".{self.function}()")
        
        if self.line_number:
            parts.append(f":{self.line_number}")
        
        parts.append(f"- {self.message}")
        
        return " ".join(parts)
    
    def __repr__(self) -> str:
        """字符串表示"""
        return f"<SystemLog(id={self.id}, level={self.level.value}, module='{self.module}')>"
    
    def __str__(self) -> str:
        """用户友好的字符串表示"""
        return self.get_formatted_message()
