"""
任务数据模型

定义任务相关的数据模型类。
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy import String, Text, Enum, Integer, JSON, ForeignKey, Index, DateTime
from sqlalchemy.orm import Mapped, mapped_column, relationship
import enum

from .base import BaseModel, AuditMixin


class TaskStatus(enum.Enum):
    """任务状态枚举"""
    PENDING = "PENDING"      # 待执行
    RUNNING = "RUNNING"      # 执行中
    SUCCESS = "SUCCESS"      # 执行成功
    FAILED = "FAILED"        # 执行失败
    CANCELLED = "CANCELLED"  # 已取消
    TIMEOUT = "TIMEOUT"      # 超时
    PAUSED = "PAUSED"        # 已暂停


class TaskPriority(enum.Enum):
    """任务优先级枚举"""
    LOW = "LOW"
    NORMAL = "NORMAL"
    HIGH = "HIGH"
    URGENT = "URGENT"


class ScheduleType(enum.Enum):
    """调度类型枚举"""
    ONCE = "ONCE"            # 一次性
    INTERVAL = "INTERVAL"    # 间隔执行
    CRON = "CRON"           # Cron表达式
    MANUAL = "MANUAL"        # 手动执行


class Task(BaseModel, AuditMixin):
    """
    任务模型
    
    存储任务的基本信息、配置和调度信息。
    """
    __tablename__ = "tasks"
    
    # 基本信息
    name: Mapped[str] = mapped_column(
        String(200),
        nullable=False,
        comment="任务名称"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="任务描述"
    )
    
    status: Mapped[TaskStatus] = mapped_column(
        Enum(TaskStatus),
        default=TaskStatus.PENDING,
        nullable=False,
        comment="任务状态"
    )
    
    priority: Mapped[TaskPriority] = mapped_column(
        Enum(TaskPriority),
        default=TaskPriority.NORMAL,
        nullable=False,
        comment="任务优先级"
    )
    
    # 脚本关联
    script_id: Mapped[int] = mapped_column(
        ForeignKey("scripts.id"),
        nullable=False,
        comment="关联脚本ID"
    )
    
    # 执行参数
    parameters: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        comment="执行参数"
    )
    
    # 调度配置
    schedule_type: Mapped[ScheduleType] = mapped_column(
        Enum(ScheduleType),
        default=ScheduleType.MANUAL,
        nullable=False,
        comment="调度类型"
    )
    
    schedule_config: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        comment="调度配置"
    )
    
    # 执行配置
    timeout: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        comment="超时时间（秒）"
    )
    
    max_retries: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        comment="最大重试次数"
    )
    
    retry_interval: Mapped[int] = mapped_column(
        Integer,
        default=60,
        nullable=False,
        comment="重试间隔（秒）"
    )
    
    # 执行时间
    next_run_time: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="下次执行时间"
    )
    
    last_run_time: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="上次执行时间"
    )
    
    # 统计信息
    execution_count: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        comment="执行次数"
    )
    
    success_count: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        comment="成功次数"
    )
    
    # 标签和分类
    tags: Mapped[Optional[List[str]]] = mapped_column(
        JSON,
        nullable=True,
        comment="任务标签"
    )
    
    category: Mapped[Optional[str]] = mapped_column(
        String(100),
        nullable=True,
        comment="任务分类"
    )
    
    # 通知配置
    notification_config: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        comment="通知配置"
    )
    
    # 关系定义
    script: Mapped["Script"] = relationship(
        "Script",
        back_populates="tasks"
    )
    
    executions: Mapped[List["Execution"]] = relationship(
        "Execution",
        back_populates="task",
        cascade="all, delete-orphan",
        order_by="Execution.created_at.desc()"
    )
    
    queue_items: Mapped[List["TaskQueue"]] = relationship(
        "TaskQueue",
        back_populates="task",
        cascade="all, delete-orphan"
    )
    
    # 索引定义 - 优化高频查询字段
    __table_args__ = (
        # 单字段索引
        Index('idx_task_name', 'name'),
        Index('idx_task_status', 'status'),
        Index('idx_task_priority', 'priority'),
        Index('idx_task_script_id', 'script_id'),
        Index('idx_task_schedule_type', 'schedule_type'),
        Index('idx_task_next_run_time', 'next_run_time'),
        Index('idx_task_category', 'category'),
        Index('idx_task_created_at', 'created_at'),
        Index('idx_task_updated_at', 'updated_at'),
        Index('idx_task_last_run_time', 'last_run_time'),
        Index('idx_task_is_deleted', 'is_deleted'),

        # 复合索引 - 优化常用查询组合
        Index('idx_task_status_priority', 'status', 'priority'),
        Index('idx_task_status_schedule_type', 'status', 'schedule_type'),
        Index('idx_task_category_status', 'category', 'status'),
        Index('idx_task_created_at_status', 'created_at', 'status'),
        Index('idx_task_next_run_time_status', 'next_run_time', 'status'),
        Index('idx_task_is_deleted_status', 'is_deleted', 'status'),

        # 覆盖索引 - 包含常用查询字段
        Index('idx_task_list_query', 'status', 'category', 'priority', 'created_at'),
        Index('idx_task_schedule_query', 'schedule_type', 'status', 'next_run_time'),
    )
    
    def __init__(self, **kwargs):
        """初始化任务实例"""
        super().__init__(**kwargs)
        if not self.parameters:
            self.parameters = {}
        if not self.schedule_config:
            self.schedule_config = {}
        if not self.tags:
            self.tags = []
        if not self.notification_config:
            self.notification_config = {}
    
    def add_tag(self, tag: str) -> None:
        """添加标签"""
        if not self.tags:
            self.tags = []
        if tag not in self.tags:
            self.tags.append(tag)
    
    def remove_tag(self, tag: str) -> None:
        """移除标签"""
        if self.tags and tag in self.tags:
            self.tags.remove(tag)
    
    def increment_execution_count(self, success: bool = True) -> None:
        """增加执行计数"""
        self.execution_count += 1
        if success:
            self.success_count += 1
        self.last_run_time = datetime.now()
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.execution_count == 0:
            return 0.0
        return (self.success_count / self.execution_count) * 100
    
    @property
    def is_schedulable(self) -> bool:
        """是否可调度"""
        return (self.status in [TaskStatus.PENDING, TaskStatus.PAUSED] and 
                not self.is_deleted and 
                self.schedule_type != ScheduleType.MANUAL)
    
    @property
    def is_executable(self) -> bool:
        """是否可执行"""
        return (self.status in [TaskStatus.PENDING, TaskStatus.PAUSED] and 
                not self.is_deleted)
    
    def can_retry(self) -> bool:
        """是否可以重试"""
        return (self.status == TaskStatus.FAILED and 
                self.execution_count <= self.max_retries)
    
    def get_effective_timeout(self) -> int:
        """获取有效的超时时间"""
        if self.timeout:
            return self.timeout
        elif self.script and self.script.timeout:
            return self.script.timeout
        else:
            return 3600  # 默认1小时
    
    def update_next_run_time(self, next_time: Optional[datetime] = None) -> None:
        """更新下次执行时间"""
        self.next_run_time = next_time
    
    def pause(self) -> None:
        """暂停任务"""
        if self.status in [TaskStatus.PENDING]:
            self.status = TaskStatus.PAUSED
    
    def resume(self) -> None:
        """恢复任务"""
        if self.status == TaskStatus.PAUSED:
            self.status = TaskStatus.PENDING
    
    def cancel(self) -> None:
        """取消任务"""
        if self.status in [TaskStatus.PENDING, TaskStatus.PAUSED]:
            self.status = TaskStatus.CANCELLED
    
    def __repr__(self) -> str:
        """字符串表示"""
        return f"<Task(id={self.id}, name='{self.name}', status={self.status.value})>"
    
    def __str__(self) -> str:
        """用户友好的字符串表示"""
        return f"Task: {self.name} ({self.status.value})"
