"""
数据库基础模型类

提供所有数据模型的基础类和通用功能。
"""

from datetime import datetime
from typing import Any, Dict, Optional
from sqlalchemy import DateTime, <PERSON><PERSON><PERSON>, Integer, String
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column
from sqlalchemy.sql import func

from ..utils.logger import get_logger

logger = get_logger(__name__)


class Base(DeclarativeBase):
    """SQLAlchemy声明式基类"""
    pass


class BaseModel(Base):
    """
    数据库基础模型类
    
    提供所有数据模型的通用字段和方法。
    """
    __abstract__ = True
    
    # 主键
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    
    # 时间戳字段
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="创建时间"
    )
    
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )
    
    # 软删除标记
    is_deleted: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        comment="是否已删除"
    )
    
    def to_dict(self, exclude_fields: Optional[list] = None) -> Dict[str, Any]:
        """
        将模型实例转换为字典
        
        Args:
            exclude_fields: 要排除的字段列表
            
        Returns:
            模型数据字典
        """
        exclude_fields = exclude_fields or []
        result = {}
        
        for column in self.__table__.columns:
            if column.name not in exclude_fields:
                value = getattr(self, column.name)
                # 处理datetime对象
                if isinstance(value, datetime):
                    result[column.name] = value.isoformat()
                else:
                    result[column.name] = value
        
        return result
    
    def update_from_dict(self, data: Dict[str, Any], 
                        exclude_fields: Optional[list] = None) -> None:
        """
        从字典更新模型实例
        
        Args:
            data: 更新数据字典
            exclude_fields: 要排除的字段列表
        """
        exclude_fields = exclude_fields or ['id', 'created_at']
        
        for key, value in data.items():
            if (key not in exclude_fields and 
                hasattr(self, key) and 
                key in [c.name for c in self.__table__.columns]):
                setattr(self, key, value)
    
    def soft_delete(self) -> None:
        """软删除记录"""
        self.is_deleted = True
        self.updated_at = datetime.now()
    
    def restore(self) -> None:
        """恢复软删除的记录"""
        self.is_deleted = False
        self.updated_at = datetime.now()
    
    @classmethod
    def get_table_name(cls) -> str:
        """获取表名"""
        return cls.__tablename__
    
    @classmethod
    def get_columns(cls) -> list:
        """获取所有列名"""
        return [column.name for column in cls.__table__.columns]
    
    def __repr__(self) -> str:
        """字符串表示"""
        return f"<{self.__class__.__name__}(id={self.id})>"
    
    def __str__(self) -> str:
        """用户友好的字符串表示"""
        if hasattr(self, 'name'):
            return f"{self.__class__.__name__}: {self.name}"
        return f"{self.__class__.__name__}(id={self.id})"


class TimestampMixin:
    """时间戳混入类"""
    
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="创建时间"
    )
    
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )


class SoftDeleteMixin:
    """软删除混入类"""
    
    is_deleted: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        comment="是否已删除"
    )
    
    def soft_delete(self) -> None:
        """软删除记录"""
        self.is_deleted = True
        if hasattr(self, 'updated_at'):
            self.updated_at = datetime.now()
    
    def restore(self) -> None:
        """恢复软删除的记录"""
        self.is_deleted = False
        if hasattr(self, 'updated_at'):
            self.updated_at = datetime.now()


class AuditMixin:
    """审计混入类"""
    
    created_by: Mapped[Optional[str]] = mapped_column(
        String(50),
        nullable=True,
        comment="创建者"
    )
    
    updated_by: Mapped[Optional[str]] = mapped_column(
        String(50),
        nullable=True,
        comment="更新者"
    )
    
    def set_audit_info(self, user: str, is_create: bool = False) -> None:
        """设置审计信息"""
        if is_create:
            self.created_by = user
        self.updated_by = user
