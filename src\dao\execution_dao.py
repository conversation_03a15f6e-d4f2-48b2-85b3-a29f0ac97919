"""
执行记录数据访问对象

提供执行记录相关的数据访问操作。
"""

from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from sqlalchemy import and_, or_, func, desc
from sqlalchemy.orm import Session, joinedload

from ..models.execution import Execution, ExecutionStatus
from ..models.task import Task
from ..models.script import Script
from ..utils.logger import get_logger
from .base_dao import BaseDAO
from .exceptions import ValidationError, EntityNotFoundError

logger = get_logger(__name__)


class ExecutionDAO(BaseDAO[Execution]):
    """执行记录数据访问对象"""
    
    def __init__(self):
        """初始化ExecutionDAO"""
        super().__init__(Execution)
    
    def get_by_execution_id(self, execution_id: str) -> Optional[Execution]:
        """
        根据执行ID获取执行记录
        
        Args:
            execution_id: 执行ID
            
        Returns:
            执行记录实例或None
        """
        try:
            with self.get_session() as session:
                return session.query(Execution).filter(
                    Execution.execution_id == execution_id
                ).first()
        except Exception as e:
            logger.error(f"Error getting execution by execution_id {execution_id}: {e}")
            raise
    
    def get_by_task_id(self, task_id: int, 
                      limit: Optional[int] = None) -> List[Execution]:
        """
        根据任务ID获取执行记录列表
        
        Args:
            task_id: 任务ID
            limit: 限制返回数量
            
        Returns:
            执行记录列表
        """
        try:
            with self.get_session() as session:
                query = session.query(Execution).filter(
                    Execution.task_id == task_id
                ).order_by(desc(Execution.created_at))
                
                if limit:
                    query = query.limit(limit)
                
                return query.all()
        except Exception as e:
            logger.error(f"Error getting executions by task_id {task_id}: {e}")
            raise
    
    def get_by_script_id(self, script_id: int, 
                        limit: Optional[int] = None) -> List[Execution]:
        """
        根据脚本ID获取执行记录列表
        
        Args:
            script_id: 脚本ID
            limit: 限制返回数量
            
        Returns:
            执行记录列表
        """
        try:
            with self.get_session() as session:
                query = session.query(Execution).filter(
                    Execution.script_id == script_id
                ).order_by(desc(Execution.created_at))
                
                if limit:
                    query = query.limit(limit)
                
                return query.all()
        except Exception as e:
            logger.error(f"Error getting executions by script_id {script_id}: {e}")
            raise
    
    def get_by_status(self, status: ExecutionStatus, 
                     limit: Optional[int] = None) -> List[Execution]:
        """
        根据状态获取执行记录列表
        
        Args:
            status: 执行状态
            limit: 限制返回数量
            
        Returns:
            执行记录列表
        """
        try:
            with self.get_session() as session:
                query = session.query(Execution).filter(
                    Execution.status == status
                ).order_by(desc(Execution.created_at))
                
                if limit:
                    query = query.limit(limit)
                
                return query.all()
        except Exception as e:
            logger.error(f"Error getting executions by status {status}: {e}")
            raise
    
    def get_running_executions(self) -> List[Execution]:
        """
        获取正在运行的执行记录
        
        Returns:
            正在运行的执行记录列表
        """
        return self.get_by_status(ExecutionStatus.RUNNING)
    
    def get_recent_executions(self, hours: int = 24, 
                             limit: Optional[int] = None) -> List[Execution]:
        """
        获取最近的执行记录
        
        Args:
            hours: 最近多少小时
            limit: 限制返回数量
            
        Returns:
            执行记录列表
        """
        try:
            with self.get_session() as session:
                since_time = datetime.now() - timedelta(hours=hours)
                
                query = session.query(Execution).filter(
                    Execution.created_at >= since_time
                ).order_by(desc(Execution.created_at))
                
                if limit:
                    query = query.limit(limit)
                
                return query.all()
        except Exception as e:
            logger.error(f"Error getting recent executions: {e}")
            raise
    
    def get_executions_by_date_range(self, start_date: datetime, 
                                   end_date: datetime) -> List[Execution]:
        """
        根据日期范围获取执行记录
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            执行记录列表
        """
        try:
            with self.get_session() as session:
                return session.query(Execution).filter(
                    and_(
                        Execution.created_at >= start_date,
                        Execution.created_at <= end_date
                    )
                ).order_by(desc(Execution.created_at)).all()
        except Exception as e:
            logger.error(f"Error getting executions by date range: {e}")
            raise
    
    def get_failed_executions(self, limit: Optional[int] = None) -> List[Execution]:
        """
        获取失败的执行记录
        
        Args:
            limit: 限制返回数量
            
        Returns:
            失败的执行记录列表
        """
        return self.get_by_status(ExecutionStatus.FAILED, limit)
    
    def get_successful_executions(self, limit: Optional[int] = None) -> List[Execution]:
        """
        获取成功的执行记录
        
        Args:
            limit: 限制返回数量
            
        Returns:
            成功的执行记录列表
        """
        return self.get_by_status(ExecutionStatus.SUCCESS, limit)
    
    def get_executions_with_details(self, limit: Optional[int] = None) -> List[Execution]:
        """
        获取包含任务和脚本详情的执行记录
        
        Args:
            limit: 限制返回数量
            
        Returns:
            执行记录列表（预加载任务和脚本信息）
        """
        try:
            with self.get_session() as session:
                query = session.query(Execution).options(
                    joinedload(Execution.task),
                    joinedload(Execution.script)
                ).order_by(desc(Execution.created_at))
                
                if limit:
                    query = query.limit(limit)
                
                return query.all()
        except Exception as e:
            logger.error(f"Error getting executions with details: {e}")
            raise
    
    def get_statistics(self, days: int = 30) -> Dict[str, Any]:
        """
        获取执行统计信息
        
        Args:
            days: 统计最近多少天的数据
            
        Returns:
            统计信息字典
        """
        try:
            with self.get_session() as session:
                since_date = datetime.now() - timedelta(days=days)
                
                # 总数统计
                total_count = session.query(Execution).filter(
                    Execution.created_at >= since_date
                ).count()
                
                # 按状态统计
                status_stats = {}
                for status in ExecutionStatus:
                    count = session.query(Execution).filter(
                        and_(
                            Execution.status == status,
                            Execution.created_at >= since_date
                        )
                    ).count()
                    status_stats[status.value] = count
                
                # 成功率计算
                success_count = status_stats.get('SUCCESS', 0)
                success_rate = 0.0
                if total_count > 0:
                    success_rate = (success_count / total_count) * 100
                
                # 平均执行时间
                avg_duration = session.query(func.avg(Execution.duration)).filter(
                    and_(
                        Execution.duration.isnot(None),
                        Execution.created_at >= since_date
                    )
                ).scalar() or 0.0
                
                # 最长执行时间
                max_duration = session.query(func.max(Execution.duration)).filter(
                    and_(
                        Execution.duration.isnot(None),
                        Execution.created_at >= since_date
                    )
                ).scalar() or 0.0
                
                # 每日执行统计
                daily_stats = session.query(
                    func.date(Execution.created_at).label('date'),
                    func.count(Execution.id).label('count'),
                    func.sum(func.case([(Execution.status == ExecutionStatus.SUCCESS, 1)], else_=0)).label('success_count')
                ).filter(
                    Execution.created_at >= since_date
                ).group_by(
                    func.date(Execution.created_at)
                ).order_by('date').all()
                
                daily_data = []
                for stat in daily_stats:
                    daily_data.append({
                        'date': stat.date.isoformat(),
                        'total': stat.count,
                        'success': stat.success_count,
                        'success_rate': (stat.success_count / stat.count * 100) if stat.count > 0 else 0
                    })
                
                return {
                    "period_days": days,
                    "total_count": total_count,
                    "status_stats": status_stats,
                    "success_rate": round(success_rate, 2),
                    "avg_duration": round(avg_duration, 2) if avg_duration else 0,
                    "max_duration": round(max_duration, 2) if max_duration else 0,
                    "daily_stats": daily_data
                }
        except Exception as e:
            logger.error(f"Error getting execution statistics: {e}")
            raise
    
    def get_task_execution_history(self, task_id: int, 
                                  limit: int = 10) -> List[Execution]:
        """
        获取任务的执行历史
        
        Args:
            task_id: 任务ID
            limit: 限制返回数量
            
        Returns:
            执行历史列表
        """
        return self.get_by_task_id(task_id, limit)
    
    def get_script_execution_history(self, script_id: int, 
                                   limit: int = 10) -> List[Execution]:
        """
        获取脚本的执行历史
        
        Args:
            script_id: 脚本ID
            limit: 限制返回数量
            
        Returns:
            执行历史列表
        """
        return self.get_by_script_id(script_id, limit)
    
    def update_status(self, execution_id: str, status: ExecutionStatus) -> Execution:
        """
        更新执行状态
        
        Args:
            execution_id: 执行ID
            status: 新状态
            
        Returns:
            更新后的执行记录实例
        """
        try:
            with self.get_session() as session:
                execution = session.query(Execution).filter(
                    Execution.execution_id == execution_id
                ).first()
                
                if execution:
                    execution.status = status
                    session.flush()
                    session.refresh(execution)
                    logger.debug(f"Updated execution {execution_id} status to {status}")
                    return execution
                else:
                    raise EntityNotFoundError("Execution", execution_id)
        except EntityNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Error updating execution status: {e}")
            raise
    
    def start_execution(self, execution_id: str) -> Execution:
        """
        开始执行
        
        Args:
            execution_id: 执行ID
            
        Returns:
            更新后的执行记录实例
        """
        try:
            with self.get_session() as session:
                execution = session.query(Execution).filter(
                    Execution.execution_id == execution_id
                ).first()
                
                if execution:
                    execution.start_execution()
                    session.flush()
                    session.refresh(execution)
                    logger.debug(f"Started execution {execution_id}")
                    return execution
                else:
                    raise EntityNotFoundError("Execution", execution_id)
        except EntityNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Error starting execution: {e}")
            raise
    
    def complete_execution(self, execution_id: str, success: bool = True,
                          exit_code: Optional[int] = None,
                          output: Optional[str] = None,
                          error_output: Optional[str] = None,
                          error_message: Optional[str] = None) -> Execution:
        """
        完成执行
        
        Args:
            execution_id: 执行ID
            success: 是否成功
            exit_code: 退出码
            output: 输出内容
            error_output: 错误输出
            error_message: 错误消息
            
        Returns:
            更新后的执行记录实例
        """
        try:
            with self.get_session() as session:
                execution = session.query(Execution).filter(
                    Execution.execution_id == execution_id
                ).first()
                
                if execution:
                    execution.complete_execution(
                        success=success,
                        exit_code=exit_code,
                        output=output,
                        error_output=error_output,
                        error_message=error_message
                    )
                    session.flush()
                    session.refresh(execution)
                    logger.debug(f"Completed execution {execution_id}")
                    return execution
                else:
                    raise EntityNotFoundError("Execution", execution_id)
        except EntityNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Error completing execution: {e}")
            raise
    
    def cancel_execution(self, execution_id: str) -> Execution:
        """
        取消执行
        
        Args:
            execution_id: 执行ID
            
        Returns:
            更新后的执行记录实例
        """
        try:
            with self.get_session() as session:
                execution = session.query(Execution).filter(
                    Execution.execution_id == execution_id
                ).first()
                
                if execution:
                    execution.cancel_execution()
                    session.flush()
                    session.refresh(execution)
                    logger.debug(f"Cancelled execution {execution_id}")
                    return execution
                else:
                    raise EntityNotFoundError("Execution", execution_id)
        except EntityNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Error cancelling execution: {e}")
            raise
    
    def _validate_create_data(self, data: Dict[str, Any]) -> None:
        """
        验证执行记录创建数据
        
        Args:
            data: 创建数据
            
        Raises:
            ValidationError: 验证失败
        """
        # 验证必需字段
        if not data.get('task_id'):
            raise ValidationError('task_id', data.get('task_id'), 'Task ID is required')
        
        if not data.get('script_id'):
            raise ValidationError('script_id', data.get('script_id'), 'Script ID is required')
        
        if not data.get('execution_id'):
            raise ValidationError('execution_id', data.get('execution_id'), 'Execution ID is required')
        
        # 验证执行ID唯一性
        if self.get_by_execution_id(data['execution_id']):
            raise ValidationError('execution_id', data['execution_id'], 'Execution ID already exists')
        
        # 验证任务和脚本存在性
        from .task_dao import TaskDAO
        from .script_dao import ScriptDAO
        
        task_dao = TaskDAO()
        script_dao = ScriptDAO()
        
        if not task_dao.exists(data['task_id']):
            raise ValidationError('task_id', data['task_id'], 'Task does not exist')
        
        if not script_dao.exists(data['script_id']):
            raise ValidationError('script_id', data['script_id'], 'Script does not exist')
