"""
配置数据访问对象

提供系统配置相关的数据访问操作。
"""

from typing import List, Optional, Dict, Any
from sqlalchemy import and_, or_

from ..models.config import Config, ConfigType, ConfigScope
from ..utils.logger import get_logger
from .base_dao import BaseDAO
from .exceptions import ValidationError, EntityNotFoundError

logger = get_logger(__name__)


class ConfigDAO(BaseDAO[Config]):
    """配置数据访问对象"""
    
    def __init__(self):
        """初始化ConfigDAO"""
        super().__init__(Config)
    
    def get_by_key(self, key: str) -> Optional[Config]:
        """
        根据键获取配置
        
        Args:
            key: 配置键
            
        Returns:
            配置实例或None
        """
        try:
            with self.get_session() as session:
                return session.query(Config).filter(
                    and_(
                        Config.key == key,
                        Config.is_deleted == False
                    )
                ).first()
        except Exception as e:
            logger.error(f"Error getting config by key {key}: {e}")
            raise
    
    def get_by_type(self, config_type: ConfigType, 
                   include_deleted: bool = False) -> List[Config]:
        """
        根据类型获取配置列表
        
        Args:
            config_type: 配置类型
            include_deleted: 是否包含已删除的配置
            
        Returns:
            配置列表
        """
        try:
            with self.get_session() as session:
                query = session.query(Config).filter(Config.config_type == config_type)
                
                if not include_deleted:
                    query = query.filter(Config.is_deleted == False)
                
                return query.order_by(Config.display_order, Config.name).all()
        except Exception as e:
            logger.error(f"Error getting configs by type {config_type}: {e}")
            raise
    
    def get_by_scope(self, scope: ConfigScope, 
                    include_deleted: bool = False) -> List[Config]:
        """
        根据作用域获取配置列表
        
        Args:
            scope: 配置作用域
            include_deleted: 是否包含已删除的配置
            
        Returns:
            配置列表
        """
        try:
            with self.get_session() as session:
                query = session.query(Config).filter(Config.scope == scope)
                
                if not include_deleted:
                    query = query.filter(Config.is_deleted == False)
                
                return query.order_by(Config.display_order, Config.name).all()
        except Exception as e:
            logger.error(f"Error getting configs by scope {scope}: {e}")
            raise
    
    def get_by_category(self, category: str, 
                       include_deleted: bool = False) -> List[Config]:
        """
        根据分类获取配置列表
        
        Args:
            category: 配置分类
            include_deleted: 是否包含已删除的配置
            
        Returns:
            配置列表
        """
        try:
            with self.get_session() as session:
                query = session.query(Config).filter(Config.category == category)
                
                if not include_deleted:
                    query = query.filter(Config.is_deleted == False)
                
                return query.order_by(Config.display_order, Config.name).all()
        except Exception as e:
            logger.error(f"Error getting configs by category {category}: {e}")
            raise
    
    def get_required_configs(self, include_deleted: bool = False) -> List[Config]:
        """
        获取必需的配置列表
        
        Args:
            include_deleted: 是否包含已删除的配置
            
        Returns:
            必需配置列表
        """
        try:
            with self.get_session() as session:
                query = session.query(Config).filter(Config.is_required == True)
                
                if not include_deleted:
                    query = query.filter(Config.is_deleted == False)
                
                return query.order_by(Config.display_order, Config.name).all()
        except Exception as e:
            logger.error(f"Error getting required configs: {e}")
            raise
    
    def get_readonly_configs(self, include_deleted: bool = False) -> List[Config]:
        """
        获取只读配置列表
        
        Args:
            include_deleted: 是否包含已删除的配置
            
        Returns:
            只读配置列表
        """
        try:
            with self.get_session() as session:
                query = session.query(Config).filter(Config.is_readonly == True)
                
                if not include_deleted:
                    query = query.filter(Config.is_deleted == False)
                
                return query.order_by(Config.display_order, Config.name).all()
        except Exception as e:
            logger.error(f"Error getting readonly configs: {e}")
            raise
    
    def get_sensitive_configs(self, include_deleted: bool = False) -> List[Config]:
        """
        获取敏感配置列表
        
        Args:
            include_deleted: 是否包含已删除的配置
            
        Returns:
            敏感配置列表
        """
        try:
            with self.get_session() as session:
                query = session.query(Config).filter(Config.is_sensitive == True)
                
                if not include_deleted:
                    query = query.filter(Config.is_deleted == False)
                
                return query.order_by(Config.display_order, Config.name).all()
        except Exception as e:
            logger.error(f"Error getting sensitive configs: {e}")
            raise
    
    def search_configs(self, keyword: str, 
                      include_deleted: bool = False) -> List[Config]:
        """
        搜索配置
        
        Args:
            keyword: 搜索关键词
            include_deleted: 是否包含已删除的配置
            
        Returns:
            配置列表
        """
        try:
            with self.get_session() as session:
                search_pattern = f"%{keyword}%"
                query = session.query(Config).filter(
                    or_(
                        Config.key.ilike(search_pattern),
                        Config.name.ilike(search_pattern),
                        Config.description.ilike(search_pattern),
                        Config.category.ilike(search_pattern)
                    )
                )
                
                if not include_deleted:
                    query = query.filter(Config.is_deleted == False)
                
                return query.order_by(Config.name).all()
        except Exception as e:
            logger.error(f"Error searching configs by keyword {keyword}: {e}")
            raise
    
    def get_config_value(self, key: str, default_value: Any = None) -> Any:
        """
        获取配置值

        Args:
            key: 配置键
            default_value: 默认值

        Returns:
            配置值
        """
        try:
            with self.get_session() as session:
                config = session.query(Config).filter(
                    and_(
                        Config.key == key,
                        Config.is_deleted == False
                    )
                ).first()

                if config:
                    # 在会话中直接获取值，避免延迟加载问题
                    value = config.value if config.value is not None else config.default_value
                    return value
                return default_value
        except Exception as e:
            logger.error(f"Error getting config value for key {key}: {e}")
            return default_value
    
    def set_config_value(self, key: str, value: Any) -> bool:
        """
        设置配置值
        
        Args:
            key: 配置键
            value: 配置值
            
        Returns:
            是否设置成功
        """
        try:
            with self.get_session() as session:
                config = session.query(Config).filter(
                    and_(
                        Config.key == key,
                        Config.is_deleted == False
                    )
                ).first()
                
                if config:
                    if config.set_value(value):
                        session.flush()
                        logger.debug(f"Set config value for key {key}")
                        return True
                    else:
                        logger.warning(f"Failed to set config value for key {key} (readonly or validation failed)")
                        return False
                else:
                    logger.warning(f"Config with key {key} not found")
                    return False
        except Exception as e:
            logger.error(f"Error setting config value for key {key}: {e}")
            raise
    
    def reset_config_to_default(self, key: str) -> bool:
        """
        重置配置为默认值
        
        Args:
            key: 配置键
            
        Returns:
            是否重置成功
        """
        try:
            with self.get_session() as session:
                config = session.query(Config).filter(
                    and_(
                        Config.key == key,
                        Config.is_deleted == False
                    )
                ).first()
                
                if config:
                    if config.reset_to_default():
                        session.flush()
                        logger.debug(f"Reset config to default for key {key}")
                        return True
                    else:
                        logger.warning(f"Failed to reset config for key {key} (readonly)")
                        return False
                else:
                    logger.warning(f"Config with key {key} not found")
                    return False
        except Exception as e:
            logger.error(f"Error resetting config for key {key}: {e}")
            raise
    
    def get_config_groups(self) -> Dict[str, List[Config]]:
        """
        获取按分类分组的配置
        
        Returns:
            分组配置字典
        """
        try:
            with self.get_session() as session:
                configs = session.query(Config).filter(
                    Config.is_deleted == False
                ).order_by(Config.category, Config.display_order, Config.name).all()
                
                groups = {}
                for config in configs:
                    category = config.category or "其他"
                    if category not in groups:
                        groups[category] = []
                    groups[category].append(config)
                
                return groups
        except Exception as e:
            logger.error(f"Error getting config groups: {e}")
            raise
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取配置统计信息
        
        Returns:
            统计信息字典
        """
        try:
            with self.get_session() as session:
                # 总数统计
                total_count = session.query(Config).filter(Config.is_deleted == False).count()
                
                # 按类型统计
                type_stats = {}
                for config_type in ConfigType:
                    count = session.query(Config).filter(
                        and_(
                            Config.config_type == config_type,
                            Config.is_deleted == False
                        )
                    ).count()
                    type_stats[config_type.value] = count
                
                # 按作用域统计
                scope_stats = {}
                for scope in ConfigScope:
                    count = session.query(Config).filter(
                        and_(
                            Config.scope == scope,
                            Config.is_deleted == False
                        )
                    ).count()
                    scope_stats[scope.value] = count
                
                # 属性统计
                required_count = session.query(Config).filter(
                    and_(
                        Config.is_required == True,
                        Config.is_deleted == False
                    )
                ).count()
                
                readonly_count = session.query(Config).filter(
                    and_(
                        Config.is_readonly == True,
                        Config.is_deleted == False
                    )
                ).count()
                
                sensitive_count = session.query(Config).filter(
                    and_(
                        Config.is_sensitive == True,
                        Config.is_deleted == False
                    )
                ).count()
                
                return {
                    "total_count": total_count,
                    "type_stats": type_stats,
                    "scope_stats": scope_stats,
                    "required_count": required_count,
                    "readonly_count": readonly_count,
                    "sensitive_count": sensitive_count
                }
        except Exception as e:
            logger.error(f"Error getting config statistics: {e}")
            raise
    
    def validate_config_value(self, key: str, value: Any) -> bool:
        """
        验证配置值
        
        Args:
            key: 配置键
            value: 配置值
            
        Returns:
            是否有效
        """
        config = self.get_by_key(key)
        if config:
            return config.validate_value(value)
        return False
    
    def _validate_create_data(self, data: Dict[str, Any]) -> None:
        """
        验证配置创建数据
        
        Args:
            data: 创建数据
            
        Raises:
            ValidationError: 验证失败
        """
        # 验证必需字段
        if not data.get('key'):
            raise ValidationError('key', data.get('key'), 'Key is required')
        
        if not data.get('name'):
            raise ValidationError('name', data.get('name'), 'Name is required')
        
        if not data.get('config_type'):
            raise ValidationError('config_type', data.get('config_type'), 'Config type is required')
        
        # 验证键唯一性
        if self.get_by_key(data['key']):
            raise ValidationError('key', data['key'], 'Config key already exists')
    
    def _validate_update_data(self, instance: Config, data: Dict[str, Any]) -> None:
        """
        验证配置更新数据
        
        Args:
            instance: 现有配置实例
            data: 更新数据
            
        Raises:
            ValidationError: 验证失败
        """
        # 验证键唯一性（如果更新键）
        if 'key' in data and data['key'] != instance.key:
            existing = self.get_by_key(data['key'])
            if existing and existing.id != instance.id:
                raise ValidationError('key', data['key'], 'Config key already exists')
        
        # 验证只读配置不能修改值
        if 'value' in data and instance.is_readonly:
            raise ValidationError('value', data['value'], 'Cannot modify readonly config value')
