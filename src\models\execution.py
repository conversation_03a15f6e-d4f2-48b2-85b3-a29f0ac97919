"""
执行记录数据模型

定义任务执行记录相关的数据模型类。
"""

from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy import String, Text, Enum, Integer, Float, ForeignKey, Index, DateTime, JSON
from sqlalchemy.orm import Mapped, mapped_column, relationship
import enum

from .base import BaseModel


class ExecutionStatus(enum.Enum):
    """执行状态枚举"""
    PENDING = "PENDING"      # 待执行
    RUNNING = "RUNNING"      # 执行中
    SUCCESS = "SUCCESS"      # 执行成功
    FAILED = "FAILED"        # 执行失败
    CANCELLED = "CANCELLED"  # 已取消
    TIMEOUT = "TIMEOUT"      # 超时
    KILLED = "KILLED"        # 被杀死


class Execution(BaseModel):
    """
    执行记录模型
    
    存储任务执行的详细记录和结果。
    """
    __tablename__ = "executions"
    
    # 关联信息
    task_id: Mapped[int] = mapped_column(
        ForeignKey("tasks.id"),
        nullable=False,
        comment="关联任务ID"
    )
    
    script_id: Mapped[int] = mapped_column(
        ForeignKey("scripts.id"),
        nullable=False,
        comment="关联脚本ID"
    )
    
    # 执行信息
    execution_id: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        unique=True,
        comment="执行唯一标识"
    )
    
    status: Mapped[ExecutionStatus] = mapped_column(
        Enum(ExecutionStatus),
        default=ExecutionStatus.PENDING,
        nullable=False,
        comment="执行状态"
    )
    
    # 时间信息
    start_time: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="开始时间"
    )
    
    end_time: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="结束时间"
    )
    
    duration: Mapped[Optional[float]] = mapped_column(
        Float,
        nullable=True,
        comment="执行时长（秒）"
    )
    
    # 执行参数
    parameters: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        comment="执行参数"
    )
    
    # 执行结果
    exit_code: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        comment="退出码"
    )
    
    output: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="标准输出"
    )
    
    error_output: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="错误输出"
    )
    
    error_message: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="错误消息"
    )
    
    # 资源使用情况
    resource_usage: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        comment="资源使用情况"
    )
    
    # 进程信息
    process_id: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        comment="进程ID"
    )
    
    # 执行环境
    execution_host: Mapped[Optional[str]] = mapped_column(
        String(100),
        nullable=True,
        comment="执行主机"
    )
    
    execution_user: Mapped[Optional[str]] = mapped_column(
        String(50),
        nullable=True,
        comment="执行用户"
    )
    
    # 重试信息
    retry_count: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        comment="重试次数"
    )
    
    parent_execution_id: Mapped[Optional[str]] = mapped_column(
        String(100),
        nullable=True,
        comment="父执行ID（重试时）"
    )
    
    # 触发信息
    trigger_type: Mapped[Optional[str]] = mapped_column(
        String(50),
        nullable=True,
        comment="触发类型"
    )
    
    trigger_info: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        comment="触发信息"
    )
    
    # 关系定义
    task: Mapped["Task"] = relationship(
        "Task",
        back_populates="executions"
    )
    
    script: Mapped["Script"] = relationship(
        "Script",
        back_populates="executions"
    )
    
    # 索引定义 - 优化执行记录查询性能
    __table_args__ = (
        # 单字段索引
        Index('idx_execution_task_id', 'task_id'),
        Index('idx_execution_script_id', 'script_id'),
        Index('idx_execution_status', 'status'),
        Index('idx_execution_start_time', 'start_time'),
        Index('idx_execution_end_time', 'end_time'),
        Index('idx_execution_execution_id', 'execution_id', unique=True),  # 唯一索引
        Index('idx_execution_created_at', 'created_at'),
        Index('idx_execution_duration', 'duration'),
        Index('idx_execution_exit_code', 'exit_code'),
        Index('idx_execution_retry_count', 'retry_count'),

        # 复合索引 - 优化常用查询组合
        Index('idx_execution_task_status', 'task_id', 'status'),
        Index('idx_execution_task_start_time', 'task_id', 'start_time'),
        Index('idx_execution_status_start_time', 'status', 'start_time'),
        Index('idx_execution_status_end_time', 'status', 'end_time'),
        Index('idx_execution_script_status', 'script_id', 'status'),

        # 覆盖索引 - 包含常用查询字段
        Index('idx_execution_list_query', 'task_id', 'status', 'start_time', 'duration'),
        Index('idx_execution_history_query', 'task_id', 'created_at', 'status', 'duration'),
        Index('idx_execution_performance_query', 'status', 'duration', 'start_time', 'end_time'),
    )
    
    def __init__(self, **kwargs):
        """初始化执行记录实例"""
        super().__init__(**kwargs)
        if not self.parameters:
            self.parameters = {}
        if not self.resource_usage:
            self.resource_usage = {}
        if not self.trigger_info:
            self.trigger_info = {}
    
    def start_execution(self) -> None:
        """开始执行"""
        self.status = ExecutionStatus.RUNNING
        self.start_time = datetime.now()
    
    def complete_execution(self, success: bool = True, 
                          exit_code: Optional[int] = None,
                          output: Optional[str] = None,
                          error_output: Optional[str] = None,
                          error_message: Optional[str] = None) -> None:
        """完成执行"""
        self.end_time = datetime.now()
        if self.start_time:
            self.duration = (self.end_time - self.start_time).total_seconds()
        
        self.exit_code = exit_code
        self.output = output
        self.error_output = error_output
        self.error_message = error_message
        
        if success:
            self.status = ExecutionStatus.SUCCESS
        else:
            self.status = ExecutionStatus.FAILED
    
    def cancel_execution(self) -> None:
        """取消执行"""
        self.status = ExecutionStatus.CANCELLED
        if not self.end_time:
            self.end_time = datetime.now()
            if self.start_time:
                self.duration = (self.end_time - self.start_time).total_seconds()
    
    def timeout_execution(self) -> None:
        """超时执行"""
        self.status = ExecutionStatus.TIMEOUT
        if not self.end_time:
            self.end_time = datetime.now()
            if self.start_time:
                self.duration = (self.end_time - self.start_time).total_seconds()
    
    def kill_execution(self) -> None:
        """杀死执行"""
        self.status = ExecutionStatus.KILLED
        if not self.end_time:
            self.end_time = datetime.now()
            if self.start_time:
                self.duration = (self.end_time - self.start_time).total_seconds()
    
    def update_resource_usage(self, cpu_percent: Optional[float] = None,
                            memory_mb: Optional[float] = None,
                            disk_io_mb: Optional[float] = None) -> None:
        """更新资源使用情况"""
        if not self.resource_usage:
            self.resource_usage = {}
        
        if cpu_percent is not None:
            self.resource_usage['cpu_percent'] = cpu_percent
        if memory_mb is not None:
            self.resource_usage['memory_mb'] = memory_mb
        if disk_io_mb is not None:
            self.resource_usage['disk_io_mb'] = disk_io_mb
    
    @property
    def is_running(self) -> bool:
        """是否正在运行"""
        return self.status == ExecutionStatus.RUNNING
    
    @property
    def is_completed(self) -> bool:
        """是否已完成"""
        return self.status in [
            ExecutionStatus.SUCCESS,
            ExecutionStatus.FAILED,
            ExecutionStatus.CANCELLED,
            ExecutionStatus.TIMEOUT,
            ExecutionStatus.KILLED
        ]
    
    @property
    def is_successful(self) -> bool:
        """是否成功"""
        return self.status == ExecutionStatus.SUCCESS
    
    @property
    def formatted_duration(self) -> str:
        """格式化的执行时长"""
        if not self.duration:
            return "N/A"
        
        if self.duration < 60:
            return f"{self.duration:.1f}秒"
        elif self.duration < 3600:
            minutes = self.duration / 60
            return f"{minutes:.1f}分钟"
        else:
            hours = self.duration / 3600
            return f"{hours:.1f}小时"
    
    def __repr__(self) -> str:
        """字符串表示"""
        return f"<Execution(id={self.id}, execution_id='{self.execution_id}', status={self.status.value})>"
    
    def __str__(self) -> str:
        """用户友好的字符串表示"""
        return f"Execution: {self.execution_id} ({self.status.value})"
