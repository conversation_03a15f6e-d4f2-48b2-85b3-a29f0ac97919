# 图标资源目录

该目录存放所有UI使用的图标资源。

## 图标命名规范

- 所有图标采用小写字母+下划线命名方式
- 使用描述性名称，例如：`add.png`, `delete.png`
- 尺寸统一为：16x16, 24x24, 32x32

## 图标分类

### 基础操作图标
- add.png - 添加
- delete.png - 删除
- edit.png - 编辑
- refresh.png - 刷新
- search.png - 搜索
- more.png - 更多操作

### 数据操作图标
- import.png - 导入
- export.png - 导出
- filter.png - 筛选
- sort.png - 排序

### 导航图标
- branch-open.png - 树节点展开
- branch-closed.png - 树节点折叠
- dropdown.png - 下拉箭头
- back.png - 返回
- forward.png - 前进

### 对象图标
- dataset.png - 数据集
- table.png - 数据表
- column.png - 字段
- report.png - 报表
- chart.png - 图表
- dashboard.png - 仪表板

## 使用说明

在代码中使用图标的标准方式：

```python
from PyQt6.QtGui import QIcon

icon = QIcon("app/ui/resources/icons/add.png")
button.setIcon(icon)
``` 