"""
通知历史数据模型

定义通知历史相关的数据模型类。
"""

from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from sqlalchemy import String, Text, Enum, Integer, ForeignKey, Index, DateTime, JSON
from sqlalchemy.orm import Mapped, mapped_column, relationship
import enum

from .base import BaseModel


class NotificationType(enum.Enum):
    """通知类型枚举"""
    EMAIL = "EMAIL"
    SMS = "SMS"
    WEBHOOK = "WEBHOOK"
    DESKTOP = "DESKTOP"
    SYSTEM = "SYSTEM"


class NotificationStatus(enum.Enum):
    """通知状态枚举"""
    PENDING = "PENDING"      # 待发送
    SENDING = "SENDING"      # 发送中
    SENT = "SENT"           # 已发送
    FAILED = "FAILED"        # 发送失败
    CANCELLED = "CANCELLED"  # 已取消


class NotificationPriority(enum.Enum):
    """通知优先级枚举"""
    LOW = "LOW"
    NORMAL = "NORMAL"
    HIGH = "HIGH"
    URGENT = "URGENT"


class NotificationHistory(BaseModel):
    """
    通知历史模型
    
    存储系统发送的通知历史记录。
    """
    __tablename__ = "notification_history"
    
    # 通知基本信息
    notification_type: Mapped[NotificationType] = mapped_column(
        Enum(NotificationType),
        nullable=False,
        comment="通知类型"
    )
    
    status: Mapped[NotificationStatus] = mapped_column(
        Enum(NotificationStatus),
        default=NotificationStatus.PENDING,
        nullable=False,
        comment="通知状态"
    )
    
    priority: Mapped[NotificationPriority] = mapped_column(
        Enum(NotificationPriority),
        default=NotificationPriority.NORMAL,
        nullable=False,
        comment="通知优先级"
    )
    
    # 通知内容
    title: Mapped[str] = mapped_column(
        String(500),
        nullable=False,
        comment="通知标题"
    )
    
    content: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        comment="通知内容"
    )
    
    template_name: Mapped[Optional[str]] = mapped_column(
        String(100),
        nullable=True,
        comment="模板名称"
    )
    
    template_variables: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        comment="模板变量"
    )
    
    # 接收者信息
    recipient: Mapped[str] = mapped_column(
        String(500),
        nullable=False,
        comment="接收者"
    )
    
    recipient_type: Mapped[str] = mapped_column(
        String(50),
        default="user",
        nullable=False,
        comment="接收者类型"
    )
    
    # 发送信息
    sender: Mapped[Optional[str]] = mapped_column(
        String(200),
        nullable=True,
        comment="发送者"
    )
    
    send_time: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="发送时间"
    )
    
    # 重试信息
    retry_count: Mapped[int] = mapped_column(
        Integer,
        default=0,
        nullable=False,
        comment="重试次数"
    )
    
    max_retries: Mapped[int] = mapped_column(
        Integer,
        default=3,
        nullable=False,
        comment="最大重试次数"
    )
    
    next_retry_time: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        comment="下次重试时间"
    )
    
    # 错误信息
    error_message: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="错误消息"
    )
    
    # 关联信息
    related_task_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("tasks.id"),
        nullable=True,
        comment="相关任务ID"
    )
    
    related_execution_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("executions.id"),
        nullable=True,
        comment="相关执行ID"
    )
    
    # 配置信息
    notification_config: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        comment="通知配置"
    )
    
    # 元数据
    extra_data: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON,
        nullable=True,
        comment="额外数据"
    )
    
    # 关系定义
    related_task: Mapped[Optional["Task"]] = relationship(
        "Task",
        foreign_keys=[related_task_id]
    )
    
    related_execution: Mapped[Optional["Execution"]] = relationship(
        "Execution",
        foreign_keys=[related_execution_id]
    )
    
    # 索引定义
    __table_args__ = (
        Index('idx_notification_type', 'notification_type'),
        Index('idx_notification_status', 'status'),
        Index('idx_notification_priority', 'priority'),
        Index('idx_notification_recipient', 'recipient'),
        Index('idx_notification_send_time', 'send_time'),
        Index('idx_notification_related_task_id', 'related_task_id'),
        Index('idx_notification_related_execution_id', 'related_execution_id'),
        Index('idx_notification_created_at', 'created_at'),
    )
    
    def __init__(self, **kwargs):
        """初始化通知历史实例"""
        super().__init__(**kwargs)
        if not self.template_variables:
            self.template_variables = {}
        if not self.notification_config:
            self.notification_config = {}
        if not self.extra_data:
            self.extra_data = {}
    
    def start_sending(self) -> None:
        """开始发送"""
        self.status = NotificationStatus.SENDING
        self.send_time = datetime.now()
    
    def complete_sending(self, success: bool = True, 
                        error_message: Optional[str] = None) -> None:
        """完成发送"""
        if success:
            self.status = NotificationStatus.SENT
        else:
            self.status = NotificationStatus.FAILED
            self.error_message = error_message
    
    def cancel_sending(self) -> None:
        """取消发送"""
        self.status = NotificationStatus.CANCELLED
    
    def schedule_retry(self, retry_delay: int = 300) -> bool:
        """
        安排重试
        
        Args:
            retry_delay: 重试延迟（秒）
            
        Returns:
            是否可以重试
        """
        if self.retry_count >= self.max_retries:
            return False
        
        self.retry_count += 1
        self.status = NotificationStatus.PENDING
        self.next_retry_time = datetime.now() + timedelta(seconds=retry_delay)
        return True
    
    @property
    def can_retry(self) -> bool:
        """是否可以重试"""
        return (self.status == NotificationStatus.FAILED and 
                self.retry_count < self.max_retries)
    
    @property
    def is_ready_for_sending(self) -> bool:
        """是否准备好发送"""
        if self.status != NotificationStatus.PENDING:
            return False
        
        if self.next_retry_time and self.next_retry_time > datetime.now():
            return False
        
        return True
    
    def get_formatted_content(self) -> str:
        """获取格式化的通知内容"""
        if not self.template_variables:
            return self.content
        
        # 简单的模板变量替换
        content = self.content
        for key, value in self.template_variables.items():
            placeholder = f"{{{key}}}"
            content = content.replace(placeholder, str(value))
        
        return content
    
    def add_extra_data(self, key: str, value: Any) -> None:
        """添加额外数据"""
        if not self.extra_data:
            self.extra_data = {}
        self.extra_data[key] = value
    
    def __repr__(self) -> str:
        """字符串表示"""
        return f"<NotificationHistory(id={self.id}, type={self.notification_type.value}, status={self.status.value})>"
    
    def __str__(self) -> str:
        """用户友好的字符串表示"""
        return f"Notification: {self.title} ({self.notification_type.value})"
