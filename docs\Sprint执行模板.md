# Sprint执行模板

## 📋 模板说明

本模板用于每个Sprint的执行管理，包含Sprint规划、每日跟踪、问题管理、评审和回顾等环节。团队可以复制此模板为每个Sprint创建独立的执行文档。

## 🎯 Sprint基本信息

### Sprint概览
- **Sprint编号**: Sprint X
- **Sprint目标**: [填写本Sprint的主要目标]
- **开始日期**: YYYY-MM-DD
- **结束日期**: YYYY-MM-DD
- **Sprint时长**: X周
- **团队成员**: [列出参与成员]

### Sprint目标
- [ ] 目标1: [具体描述]
- [ ] 目标2: [具体描述]
- [ ] 目标3: [具体描述]

## 📋 Sprint Backlog

### 计划任务列表

| 任务ID | 任务名称 | 负责人 | 预估工时 | 优先级 | 状态 | 备注 |
|--------|----------|--------|----------|--------|------|------|
| TX.X | [任务名称] | [姓名] | X天 | P0/P1/P2 | 待开始/进行中/已完成 | [备注] |
| | | | | | | |
| | | | | | | |

### 任务状态统计
- **总任务数**: X个
- **已完成**: X个
- **进行中**: X个
- **待开始**: X个
- **完成率**: X%

## 📅 每日站会记录

### 第1天 (YYYY-MM-DD)

#### 团队成员状态
**[成员姓名]**
- 昨日完成: [具体工作内容]
- 今日计划: [具体工作计划]
- 遇到障碍: [问题描述，无则填"无"]

**[成员姓名]**
- 昨日完成: [具体工作内容]
- 今日计划: [具体工作计划]
- 遇到障碍: [问题描述，无则填"无"]

#### 团队讨论
- [重要决策或讨论内容]
- [需要协调的事项]

#### 行动项
- [ ] [具体行动项] - 负责人: [姓名] - 截止日期: [日期]

---

### 第2天 (YYYY-MM-DD)
[按照第1天格式填写]

---

### 第X天 (YYYY-MM-DD)
[按照第1天格式填写]

## 🔥 问题和风险管理

### 当前问题列表

| 问题ID | 问题描述 | 影响级别 | 负责人 | 状态 | 解决方案 | 截止日期 |
|--------|----------|----------|--------|------|----------|----------|
| P001 | [问题描述] | 高/中/低 | [姓名] | 新建/处理中/已解决 | [解决方案] | YYYY-MM-DD |
| | | | | | | |

### 风险识别

| 风险ID | 风险描述 | 发生概率 | 影响程度 | 应对策略 | 负责人 |
|--------|----------|----------|----------|----------|--------|
| R001 | [风险描述] | 高/中/低 | 高/中/低 | [应对策略] | [姓名] |
| | | | | | |

## 📊 Sprint燃尽图

### 工作量燃尽数据

| 日期 | 剩余工时 | 理想燃尽 | 实际燃尽 | 备注 |
|------|----------|----------|----------|------|
| Day 1 | [总工时] | [理想值] | [实际值] | [备注] |
| Day 2 | | | | |
| Day X | 0 | 0 | [实际值] | Sprint结束 |

### 燃尽图分析
- **趋势分析**: [分析燃尽趋势]
- **偏差原因**: [分析偏差原因]
- **调整措施**: [采取的调整措施]

## 🎯 Sprint评审

### 功能演示清单
- [ ] 功能1: [功能名称] - 演示状态: [完成/部分完成/未完成]
- [ ] 功能2: [功能名称] - 演示状态: [完成/部分完成/未完成]
- [ ] 功能3: [功能名称] - 演示状态: [完成/部分完成/未完成]

### 演示反馈
**Product Owner反馈**:
- [反馈内容]
- [需要调整的地方]

**利益相关者反馈**:
- [反馈内容]
- [建议和意见]

### 验收结果
- **已验收功能**: [列出已验收的功能]
- **需要返工功能**: [列出需要返工的功能]
- **新增需求**: [记录新提出的需求]

## 🔄 Sprint回顾

### 做得好的地方 (Keep)
- [团队认为做得好的地方1]
- [团队认为做得好的地方2]
- [团队认为做得好的地方3]

### 需要改进的地方 (Problem)
- [需要改进的地方1]
- [需要改进的地方2]
- [需要改进的地方3]

### 改进措施 (Try)
- [具体改进措施1] - 负责人: [姓名] - 实施时间: [时间]
- [具体改进措施2] - 负责人: [姓名] - 实施时间: [时间]
- [具体改进措施3] - 负责人: [姓名] - 实施时间: [时间]

### 团队满意度评分
**整体满意度**: [1-10分]

**分项评分**:
- 团队协作: [1-10分]
- 工作量安排: [1-10分]
- 技术支持: [1-10分]
- 沟通效率: [1-10分]
- 工具支持: [1-10分]

## 📈 Sprint指标

### 开发效率指标
- **计划工时**: X人天
- **实际工时**: X人天
- **工时偏差**: +/-X%
- **任务完成率**: X%
- **缺陷发现数**: X个
- **缺陷修复率**: X%

### 质量指标
- **代码覆盖率**: X%
- **代码审查覆盖率**: X%
- **自动化测试通过率**: X%
- **性能测试结果**: [通过/不通过]

### 团队协作指标
- **每日站会参与率**: X%
- **代码提交频率**: X次/天
- **知识分享次数**: X次
- **团队决策效率**: [高/中/低]

## 📝 经验教训

### 技术经验
- [技术方面的经验教训]
- [最佳实践总结]
- [技术难点解决方案]

### 流程经验
- [流程方面的经验教训]
- [协作方式改进]
- [沟通效率提升]

### 工具经验
- [工具使用经验]
- [工具改进建议]
- [新工具评估]

## 🎯 下个Sprint准备

### 待办事项
- [ ] [下个Sprint需要准备的事项1]
- [ ] [下个Sprint需要准备的事项2]
- [ ] [下个Sprint需要准备的事项3]

### 技术准备
- [需要学习的技术]
- [需要搭建的环境]
- [需要解决的技术债务]

### 团队准备
- [团队技能提升计划]
- [角色调整计划]
- [协作方式优化]

## 📋 行动项跟踪

### 本Sprint行动项
- [ ] [行动项1] - 负责人: [姓名] - 状态: [完成/进行中/延期]
- [ ] [行动项2] - 负责人: [姓名] - 状态: [完成/进行中/延期]
- [ ] [行动项3] - 负责人: [姓名] - 状态: [完成/进行中/延期]

### 遗留行动项
- [ ] [遗留行动项1] - 负责人: [姓名] - 原因: [延期原因]
- [ ] [遗留行动项2] - 负责人: [姓名] - 原因: [延期原因]

## 📊 Sprint总结

### 目标达成情况
- **目标1**: [达成/部分达成/未达成] - [说明]
- **目标2**: [达成/部分达成/未达成] - [说明]
- **目标3**: [达成/部分达成/未达成] - [说明]

### 整体评价
**Sprint成功度**: [1-10分]

**主要成就**:
- [主要成就1]
- [主要成就2]
- [主要成就3]

**主要挑战**:
- [主要挑战1]
- [主要挑战2]
- [主要挑战3]

### 对项目整体的影响
- **进度影响**: [正面/负面/无影响] - [具体说明]
- **质量影响**: [正面/负面/无影响] - [具体说明]
- **团队影响**: [正面/负面/无影响] - [具体说明]

---

**Sprint执行人**: [姓名]  
**文档创建日期**: YYYY-MM-DD  
**最后更新日期**: YYYY-MM-DD  
**文档状态**: [进行中/已完成]
