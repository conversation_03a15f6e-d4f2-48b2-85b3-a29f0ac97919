"""
数据库初始化数据

提供系统初始化时需要的默认数据。
"""

from typing import List, Dict, Any
from sqlalchemy.orm import Session

from ..utils.logger import get_logger
from .config import Config, ConfigType, ConfigScope
from .script import Script, ScriptType, ScriptStatus

logger = get_logger(__name__)


def create_default_configs() -> List[Dict[str, Any]]:
    """
    创建默认系统配置
    
    Returns:
        默认配置数据列表
    """
    configs = [
        # 系统配置
        {
            "key": "system.app_name",
            "name": "应用名称",
            "description": "应用程序的显示名称",
            "config_type": ConfigType.SYSTEM,
            "scope": ConfigScope.GLOBAL,
            "category": "system",
            "value": "自动化任务管理工具",
            "default_value": "自动化任务管理工具",
            "is_required": True,
            "is_readonly": False,
            "is_sensitive": False,
            "validation_rules": {"type": "string", "max_length": 100},
            "display_order": 1,
            "ui_component": "text",
        },
        {
            "key": "system.app_version",
            "name": "应用版本",
            "description": "应用程序的版本号",
            "config_type": ConfigType.SYSTEM,
            "scope": ConfigScope.GLOBAL,
            "category": "system",
            "value": "1.0.0",
            "default_value": "1.0.0",
            "is_required": True,
            "is_readonly": True,
            "is_sensitive": False,
            "validation_rules": {"type": "string"},
            "display_order": 2,
            "ui_component": "text",
        },
        {
            "key": "system.debug_mode",
            "name": "调试模式",
            "description": "是否启用调试模式",
            "config_type": ConfigType.SYSTEM,
            "scope": ConfigScope.GLOBAL,
            "category": "system",
            "value": False,
            "default_value": False,
            "is_required": True,
            "is_readonly": False,
            "is_sensitive": False,
            "validation_rules": {"type": "boolean"},
            "display_order": 3,
            "ui_component": "checkbox",
        },
        
        # 执行配置
        {
            "key": "execution.max_concurrent_tasks",
            "name": "最大并发任务数",
            "description": "同时执行的最大任务数量",
            "config_type": ConfigType.EXECUTION,
            "scope": ConfigScope.GLOBAL,
            "category": "execution",
            "value": 3,
            "default_value": 3,
            "is_required": True,
            "is_readonly": False,
            "is_sensitive": False,
            "validation_rules": {"type": "integer", "min": 1, "max": 10},
            "display_order": 1,
            "ui_component": "number",
        },
        {
            "key": "execution.default_timeout",
            "name": "默认超时时间",
            "description": "任务执行的默认超时时间（秒）",
            "config_type": ConfigType.EXECUTION,
            "scope": ConfigScope.GLOBAL,
            "category": "execution",
            "value": 3600,
            "default_value": 3600,
            "is_required": True,
            "is_readonly": False,
            "is_sensitive": False,
            "validation_rules": {"type": "integer", "min": 60, "max": 86400},
            "display_order": 2,
            "ui_component": "number",
        },
        {
            "key": "execution.retry_attempts",
            "name": "默认重试次数",
            "description": "任务失败时的默认重试次数",
            "config_type": ConfigType.EXECUTION,
            "scope": ConfigScope.GLOBAL,
            "category": "execution",
            "value": 3,
            "default_value": 3,
            "is_required": True,
            "is_readonly": False,
            "is_sensitive": False,
            "validation_rules": {"type": "integer", "min": 0, "max": 10},
            "display_order": 3,
            "ui_component": "number",
        },
        
        # 安全配置
        {
            "key": "security.sandbox_enabled",
            "name": "启用沙箱",
            "description": "是否启用脚本执行沙箱",
            "config_type": ConfigType.SECURITY,
            "scope": ConfigScope.GLOBAL,
            "category": "security",
            "value": True,
            "default_value": True,
            "is_required": True,
            "is_readonly": False,
            "is_sensitive": False,
            "validation_rules": {"type": "boolean"},
            "display_order": 1,
            "ui_component": "checkbox",
        },
        {
            "key": "security.max_memory_mb",
            "name": "最大内存限制",
            "description": "脚本执行的最大内存限制（MB）",
            "config_type": ConfigType.SECURITY,
            "scope": ConfigScope.GLOBAL,
            "category": "security",
            "value": 512,
            "default_value": 512,
            "is_required": True,
            "is_readonly": False,
            "is_sensitive": False,
            "validation_rules": {"type": "integer", "min": 64, "max": 2048},
            "display_order": 2,
            "ui_component": "number",
        },
        
        # UI配置
        {
            "key": "ui.theme",
            "name": "界面主题",
            "description": "用户界面主题",
            "config_type": ConfigType.UI,
            "scope": ConfigScope.USER,
            "category": "ui",
            "value": "light",
            "default_value": "light",
            "is_required": True,
            "is_readonly": False,
            "is_sensitive": False,
            "validation_rules": {"type": "string", "options": ["light", "dark"]},
            "display_order": 1,
            "ui_component": "select",
            "ui_options": {"options": [{"value": "light", "label": "浅色"}, {"value": "dark", "label": "深色"}]},
        },
        {
            "key": "ui.language",
            "name": "界面语言",
            "description": "用户界面语言",
            "config_type": ConfigType.UI,
            "scope": ConfigScope.USER,
            "category": "ui",
            "value": "zh_CN",
            "default_value": "zh_CN",
            "is_required": True,
            "is_readonly": False,
            "is_sensitive": False,
            "validation_rules": {"type": "string", "options": ["zh_CN", "en_US"]},
            "display_order": 2,
            "ui_component": "select",
            "ui_options": {"options": [{"value": "zh_CN", "label": "中文"}, {"value": "en_US", "label": "English"}]},
        },
        
        # 通知配置
        {
            "key": "notification.email_enabled",
            "name": "启用邮件通知",
            "description": "是否启用邮件通知功能",
            "config_type": ConfigType.NOTIFICATION,
            "scope": ConfigScope.GLOBAL,
            "category": "notification",
            "value": False,
            "default_value": False,
            "is_required": True,
            "is_readonly": False,
            "is_sensitive": False,
            "validation_rules": {"type": "boolean"},
            "display_order": 1,
            "ui_component": "checkbox",
        },
        {
            "key": "notification.webhook_enabled",
            "name": "启用Webhook通知",
            "description": "是否启用Webhook通知功能",
            "config_type": ConfigType.NOTIFICATION,
            "scope": ConfigScope.GLOBAL,
            "category": "notification",
            "value": False,
            "default_value": False,
            "is_required": True,
            "is_readonly": False,
            "is_sensitive": False,
            "validation_rules": {"type": "boolean"},
            "display_order": 2,
            "ui_component": "checkbox",
        },
    ]
    
    return configs


def create_sample_scripts() -> List[Dict[str, Any]]:
    """
    创建示例脚本
    
    Returns:
        示例脚本数据列表
    """
    scripts = [
        {
            "name": "Hello World Python",
            "description": "简单的Python Hello World脚本示例",
            "script_type": ScriptType.PYTHON,
            "status": ScriptStatus.ACTIVE,
            "content": '''#!/usr/bin/env python3
"""
Hello World 示例脚本
"""

import sys
import datetime

def main():
    print("Hello, World!")
    print(f"当前时间: {datetime.datetime.now()}")
    print("这是一个示例Python脚本")
    
    # 返回成功状态
    return 0

if __name__ == "__main__":
    sys.exit(main())
''',
            "version": "1.0.0",
            "parameters": {
                "message": {
                    "type": "string",
                    "default": "Hello, World!",
                    "description": "要显示的消息",
                    "required": False
                }
            },
            "timeout": 60,
            "tags": ["示例", "Python", "Hello World"],
            "category": "示例脚本",
            "created_by": "system",
        },
        {
            "name": "系统信息检查",
            "description": "检查系统基本信息的脚本",
            "script_type": ScriptType.PYTHON,
            "status": ScriptStatus.ACTIVE,
            "content": '''#!/usr/bin/env python3
"""
系统信息检查脚本
"""

import sys
import platform
import psutil
import datetime

def main():
    print("=== 系统信息检查 ===")
    print(f"检查时间: {datetime.datetime.now()}")
    print()
    
    # 系统基本信息
    print("系统信息:")
    print(f"  操作系统: {platform.system()} {platform.release()}")
    print(f"  架构: {platform.architecture()[0]}")
    print(f"  处理器: {platform.processor()}")
    print(f"  Python版本: {platform.python_version()}")
    print()
    
    # 资源使用情况
    print("资源使用:")
    print(f"  CPU使用率: {psutil.cpu_percent(interval=1):.1f}%")
    
    memory = psutil.virtual_memory()
    print(f"  内存使用: {memory.percent:.1f}% ({memory.used // 1024 // 1024}MB / {memory.total // 1024 // 1024}MB)")
    
    disk = psutil.disk_usage('/')
    print(f"  磁盘使用: {disk.percent:.1f}% ({disk.used // 1024 // 1024 // 1024}GB / {disk.total // 1024 // 1024 // 1024}GB)")
    
    print()
    print("系统信息检查完成")
    return 0

if __name__ == "__main__":
    sys.exit(main())
''',
            "version": "1.0.0",
            "parameters": {},
            "timeout": 120,
            "tags": ["系统", "监控", "信息"],
            "category": "系统工具",
            "created_by": "system",
        }
    ]
    
    return scripts


def init_default_data(session: Session) -> None:
    """
    初始化默认数据
    
    Args:
        session: 数据库会话
    """
    logger.info("开始初始化默认数据...")
    
    try:
        # 检查是否已经初始化过
        existing_configs = session.query(Config).filter(Config.key == "system.app_name").first()
        if existing_configs:
            logger.info("默认数据已存在，跳过初始化")
            return
        
        # 创建默认配置
        logger.info("创建默认系统配置...")
        config_data = create_default_configs()
        for config_item in config_data:
            config = Config(**config_item)
            session.add(config)
        
        # 创建示例脚本
        logger.info("创建示例脚本...")
        script_data = create_sample_scripts()
        for script_item in script_data:
            script = Script(**script_item)
            session.add(script)
        
        # 提交事务
        session.commit()
        logger.info("默认数据初始化完成")
        
    except Exception as e:
        session.rollback()
        logger.error(f"默认数据初始化失败: {e}")
        raise
