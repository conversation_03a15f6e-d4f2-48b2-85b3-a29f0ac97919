# 自动化任务管理工具系统架构设计规范

## 📋 文档概述

### 文档目的
本文档详细描述了自动化任务管理工具的系统架构设计，包括技术选型、架构模式、模块设计、接口定义和部署方案，为开发团队提供完整的技术实现指导。

### 文档定位与适用场景
- **文档类型**: 系统架构设计规范文档
- **侧重点**: 架构模式、设计原则、接口规范、模块划分
- **目标读者**: 系统架构师、技术负责人、高级开发工程师
- **使用场景**:
  - 系统架构设计和评审
  - 模块接口定义和规范制定
  - 技术选型决策参考
  - 代码架构指导和约束
- **配套文档**: 与《技术架构设计.md》互补，后者侧重具体技术实现细节

### 架构目标
- **模块化设计**: 系统采用模块化设计，便于维护和扩展
- **松耦合**: 模块间松耦合，降低系统复杂度
- **高性能**: 优化系统性能，提供良好的用户体验
- **可扩展**: 支持功能扩展和系统升级
- **安全性**: 确保系统和数据安全

### 设计原则
- **单一职责**: 每个模块只负责一个特定功能
- **开闭原则**: 对扩展开放，对修改关闭
- **依赖倒置**: 依赖抽象而不是具体实现
- **接口隔离**: 使用专门的接口，避免依赖不需要的接口

## 📑 目录

1. [技术栈选型](#技术栈选型)
2. [整体架构设计](#整体架构设计)
3. [模块设计](#模块设计)
4. [数据库设计](#数据库设计)
5. [接口设计](#接口设计)
6. [安全架构](#安全架构)
7. [部署架构](#部署架构)
8. [性能优化](#性能优化)

## 🛠️ 技术栈选型

### 1.1 前端技术栈

#### UI框架选择
```python
# PyQt6 - 现代化UI框架
PyQt6==6.6.0
PyQt6-Qt6==6.6.0
PyQt6-sip==13.6.0

# 图表组件
PyQtChart==6.6.0

# 代码编辑器
QScintilla==2.14.1
```

**选择理由**:
- PyQt6提供现代化UI组件和更好的高DPI支持
- 丰富的控件库和强大的样式系统
- 优秀的跨平台兼容性
- 活跃的社区支持

#### 样式和主题
```css
/* QSS样式系统 */
- 支持CSS-like语法
- 动态主题切换
- 响应式设计支持
- 自定义组件样式
```

### 1.2 后端技术栈

#### 核心框架
```python
# Python核心
Python>=3.10

# 异步处理
asyncio
concurrent.futures

# 多线程/多进程
threading
multiprocessing
queue

# 任务调度
APScheduler==3.10.4
schedule==1.2.0
```

#### 数据处理
```python
# ORM框架
SQLAlchemy==2.0.23
alembic==1.13.1

# 数据库驱动
sqlite3 (内置)

# 数据序列化
pickle
json
yaml
```

#### 系统集成
```python
# 系统监控
psutil==5.9.6

# Windows集成
pywin32==306
winreg (内置)

# 网络通信
requests==2.31.0
websockets==12.0

# 安全组件
cryptography==41.0.7
RestrictedPython==6.2
```

### 1.3 开发工具链

#### 开发环境
```python
# 代码质量
flake8==6.1.0
black==23.11.0
mypy==1.7.1

# 测试框架
pytest==7.4.3
pytest-qt==4.2.0
pytest-cov==4.1.0
pytest-asyncio==0.21.1

# 文档生成
sphinx==7.2.6
sphinx-rtd-theme==1.3.0
```

#### 构建打包
```python
# 应用打包
PyInstaller==6.2.0
cx_Freeze==6.15.10

# 安装程序
NSIS==3.09
Inno Setup==6.2.2

# 依赖管理
pip-tools==7.3.0
poetry==1.7.1
```

## 🏗️ 整体架构设计

### 2.1 架构模式

#### 三层架构
```
┌─────────────────────────────────────────────────────────────┐
│                   表示层 (Presentation Layer)               │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  │
│  │  MainWindow │  │  TaskWidget │  │ ScriptWidget│  │SettingsWidget│  │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                  业务逻辑层 (Business Layer)                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  │
│  │ TaskService │  │ScriptService│  │ExecutionSvc │  │ SystemSvc   │  │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                  数据访问层 (Data Access Layer)              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  │
│  │   TaskDAO   │  │  ScriptDAO  │  │   LogDAO    │  │  ConfigDAO  │  │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                    数据存储层 (Data Layer)                   │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  │
│  │ SQLite DB   │  │ File System │  │ Log Files   │  │ Config Files│  │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

#### 事件驱动架构
```python
# 事件总线设计
class EventBus:
    def __init__(self):
        self._subscribers = {}
    
    def subscribe(self, event_type: str, callback: callable):
        """订阅事件"""
        if event_type not in self._subscribers:
            self._subscribers[event_type] = []
        self._subscribers[event_type].append(callback)
    
    def publish(self, event_type: str, data: dict):
        """发布事件"""
        if event_type in self._subscribers:
            for callback in self._subscribers[event_type]:
                callback(data)

# 事件类型定义
class EventTypes:
    TASK_CREATED = "task.created"
    TASK_UPDATED = "task.updated"
    TASK_DELETED = "task.deleted"
    TASK_STARTED = "task.started"
    TASK_COMPLETED = "task.completed"
    TASK_FAILED = "task.failed"
    
    SCRIPT_CREATED = "script.created"
    SCRIPT_UPDATED = "script.updated"
    SCRIPT_DELETED = "script.deleted"
    
    SYSTEM_STARTUP = "system.startup"
    SYSTEM_SHUTDOWN = "system.shutdown"
```

### 2.2 模块依赖关系

#### 依赖图
```
Application
    ├── UI Layer
    │   ├── MainWindow
    │   ├── TaskWidget
    │   ├── ScriptWidget
    │   └── SettingsWidget
    │
    ├── Service Layer
    │   ├── TaskService
    │   ├── ScriptService
    │   ├── ExecutionService
    │   ├── SchedulerService
    │   ├── LogService
    │   └── ConfigService
    │
    ├── DAO Layer
    │   ├── TaskDAO
    │   ├── ScriptDAO
    │   ├── ExecutionLogDAO
    │   └── ConfigDAO
    │
    ├── Core Components
    │   ├── Database
    │   ├── EventBus
    │   ├── Logger
    │   ├── Security
    │   └── Utils
    │
    └── External Dependencies
        ├── PyQt6
        ├── SQLAlchemy
        ├── APScheduler
        └── psutil
```

### 2.3 通信机制

#### 信号槽机制
```python
# PyQt6信号槽系统
from PyQt6.QtCore import QObject, pyqtSignal, pyqtSlot

class TaskService(QObject):
    # 定义信号
    task_created = pyqtSignal(dict)
    task_updated = pyqtSignal(dict)
    task_deleted = pyqtSignal(int)
    task_status_changed = pyqtSignal(int, str)
    
    def create_task(self, task_data: dict):
        # 创建任务逻辑
        task = self._create_task_impl(task_data)
        # 发射信号
        self.task_created.emit(task)
        return task

class TaskWidget(QWidget):
    def __init__(self, task_service: TaskService):
        super().__init__()
        self.task_service = task_service
        # 连接信号槽
        self.task_service.task_created.connect(self.on_task_created)
        self.task_service.task_updated.connect(self.on_task_updated)
    
    @pyqtSlot(dict)
    def on_task_created(self, task_data: dict):
        # 处理任务创建事件
        self.refresh_task_list()
```

#### 异步消息队列
```python
import asyncio
from queue import Queue
from threading import Thread

class MessageQueue:
    def __init__(self):
        self._queue = Queue()
        self._running = False
        self._worker_thread = None
    
    def start(self):
        self._running = True
        self._worker_thread = Thread(target=self._worker)
        self._worker_thread.start()
    
    def stop(self):
        self._running = False
        if self._worker_thread:
            self._worker_thread.join()
    
    def put_message(self, message: dict):
        self._queue.put(message)
    
    def _worker(self):
        while self._running:
            try:
                message = self._queue.get(timeout=1)
                self._process_message(message)
            except:
                continue
    
    def _process_message(self, message: dict):
        # 处理消息逻辑
        pass
```

## 🧩 模块设计

### 3.1 表示层模块

#### MainWindow - 主窗口
```python
class MainWindow(QMainWindow):
    """主窗口类 - 应用程序入口"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_services()
        self.setup_connections()
    
    def setup_ui(self):
        """初始化UI组件"""
        # 创建菜单栏
        self.create_menu_bar()
        # 创建工具栏
        self.create_tool_bar()
        # 创建状态栏
        self.create_status_bar()
        # 创建中央窗口
        self.create_central_widget()
    
    def setup_services(self):
        """初始化服务层"""
        self.task_service = TaskService()
        self.script_service = ScriptService()
        self.execution_service = ExecutionService()
    
    def setup_connections(self):
        """建立信号槽连接"""
        # 连接服务层信号
        pass
```

#### TaskWidget - 任务管理组件
```python
class TaskWidget(QWidget):
    """任务管理组件"""
    
    def __init__(self, task_service: TaskService):
        super().__init__()
        self.task_service = task_service
        self.setup_ui()
        self.setup_connections()
    
    def setup_ui(self):
        """初始化UI"""
        # 创建工具栏
        self.create_toolbar()
        # 创建任务列表
        self.create_task_list()
        # 创建分页控件
        self.create_pagination()
    
    def create_toolbar(self):
        """创建工具栏"""
        self.toolbar = QHBoxLayout()
        
        # 新建按钮
        self.new_btn = QPushButton("新建任务")
        self.new_btn.clicked.connect(self.create_task)
        
        # 搜索框
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("搜索任务...")
        self.search_input.textChanged.connect(self.filter_tasks)
        
        self.toolbar.addWidget(self.new_btn)
        self.toolbar.addWidget(self.search_input)
```

### 3.2 业务逻辑层模块

#### TaskService - 任务服务
```python
class TaskService(QObject):
    """任务管理服务"""
    
    # 信号定义
    task_created = pyqtSignal(dict)
    task_updated = pyqtSignal(dict)
    task_deleted = pyqtSignal(int)
    task_status_changed = pyqtSignal(int, str)
    
    def __init__(self, task_dao: TaskDAO):
        super().__init__()
        self.task_dao = task_dao
        self.event_bus = EventBus.instance()
    
    def create_task(self, task_data: dict) -> Task:
        """创建任务"""
        # 验证任务数据
        self._validate_task_data(task_data)
        
        # 创建任务对象
        task = Task(**task_data)
        
        # 保存到数据库
        task_id = self.task_dao.create(task)
        task.id = task_id
        
        # 发布事件
        self.event_bus.publish(EventTypes.TASK_CREATED, task.to_dict())
        self.task_created.emit(task.to_dict())
        
        return task
    
    def update_task(self, task_id: int, task_data: dict) -> Task:
        """更新任务"""
        # 验证任务存在
        task = self.task_dao.get_by_id(task_id)
        if not task:
            raise TaskNotFoundError(f"Task {task_id} not found")
        
        # 更新任务数据
        for key, value in task_data.items():
            setattr(task, key, value)
        
        # 保存到数据库
        self.task_dao.update(task)
        
        # 发布事件
        self.event_bus.publish(EventTypes.TASK_UPDATED, task.to_dict())
        self.task_updated.emit(task.to_dict())
        
        return task
    
    def delete_task(self, task_id: int):
        """删除任务"""
        # 检查任务是否正在运行
        if self._is_task_running(task_id):
            raise TaskRunningError("Cannot delete running task")
        
        # 软删除任务
        self.task_dao.soft_delete(task_id)
        
        # 发布事件
        self.event_bus.publish(EventTypes.TASK_DELETED, {"task_id": task_id})
        self.task_deleted.emit(task_id)
    
    def get_tasks(self, filters: dict = None, page: int = 1, size: int = 20) -> List[Task]:
        """获取任务列表"""
        return self.task_dao.get_list(filters, page, size)
    
    def _validate_task_data(self, task_data: dict):
        """验证任务数据"""
        required_fields = ['name', 'script_id', 'schedule_type']
        for field in required_fields:
            if field not in task_data:
                raise ValidationError(f"Missing required field: {field}")
    
    def _is_task_running(self, task_id: int) -> bool:
        """检查任务是否正在运行"""
        # 实现检查逻辑
        pass
```

#### ExecutionService - 执行服务
```python
class ExecutionService(QObject):
    """任务执行服务"""
    
    # 信号定义
    execution_started = pyqtSignal(int, int)  # task_id, execution_id
    execution_progress = pyqtSignal(int, int, int)  # task_id, execution_id, progress
    execution_completed = pyqtSignal(int, int, dict)  # task_id, execution_id, result
    execution_failed = pyqtSignal(int, int, str)  # task_id, execution_id, error
    
    def __init__(self):
        super().__init__()
        self.running_tasks = {}
        self.thread_pool = QThreadPool()
        self.thread_pool.setMaxThreadCount(3)  # 最大并发数
    
    def execute_task(self, task: Task) -> int:
        """执行任务"""
        # 创建执行记录
        execution = ExecutionLog(
            task_id=task.id,
            status='RUNNING',
            start_time=datetime.now()
        )
        execution_id = self.execution_dao.create(execution)
        
        # 创建执行线程
        worker = TaskExecutionWorker(task, execution_id)
        worker.signals.started.connect(self.on_execution_started)
        worker.signals.progress.connect(self.on_execution_progress)
        worker.signals.completed.connect(self.on_execution_completed)
        worker.signals.failed.connect(self.on_execution_failed)
        
        # 提交到线程池
        self.thread_pool.start(worker)
        
        # 记录运行状态
        self.running_tasks[execution_id] = {
            'task': task,
            'worker': worker,
            'start_time': datetime.now()
        }
        
        return execution_id
    
    def stop_task(self, execution_id: int):
        """停止任务执行"""
        if execution_id in self.running_tasks:
            worker = self.running_tasks[execution_id]['worker']
            worker.stop()
    
    @pyqtSlot(int, int)
    def on_execution_started(self, task_id: int, execution_id: int):
        """任务开始执行"""
        self.execution_started.emit(task_id, execution_id)
    
    @pyqtSlot(int, int, int)
    def on_execution_progress(self, task_id: int, execution_id: int, progress: int):
        """任务执行进度"""
        self.execution_progress.emit(task_id, execution_id, progress)
    
    @pyqtSlot(int, int, dict)
    def on_execution_completed(self, task_id: int, execution_id: int, result: dict):
        """任务执行完成"""
        # 更新执行记录
        execution = self.execution_dao.get_by_id(execution_id)
        execution.status = 'COMPLETED'
        execution.end_time = datetime.now()
        execution.result = result
        self.execution_dao.update(execution)
        
        # 清理运行状态
        if execution_id in self.running_tasks:
            del self.running_tasks[execution_id]
        
        self.execution_completed.emit(task_id, execution_id, result)
    
    @pyqtSlot(int, int, str)
    def on_execution_failed(self, task_id: int, execution_id: int, error: str):
        """任务执行失败"""
        # 更新执行记录
        execution = self.execution_dao.get_by_id(execution_id)
        execution.status = 'FAILED'
        execution.end_time = datetime.now()
        execution.error_message = error
        self.execution_dao.update(execution)
        
        # 清理运行状态
        if execution_id in self.running_tasks:
            del self.running_tasks[execution_id]
        
        self.execution_failed.emit(task_id, execution_id, error)
```

### 3.3 数据访问层模块

#### BaseDAO - 基础数据访问对象
```python
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any

class BaseDAO:
    """基础DAO类"""
    
    def __init__(self, session: Session, model_class):
        self.session = session
        self.model_class = model_class
    
    def create(self, obj) -> int:
        """创建对象"""
        self.session.add(obj)
        self.session.commit()
        return obj.id
    
    def get_by_id(self, obj_id: int) -> Optional[Any]:
        """根据ID获取对象"""
        return self.session.query(self.model_class).filter(
            self.model_class.id == obj_id
        ).first()
    
    def update(self, obj) -> None:
        """更新对象"""
        self.session.merge(obj)
        self.session.commit()
    
    def delete(self, obj_id: int) -> None:
        """删除对象"""
        obj = self.get_by_id(obj_id)
        if obj:
            self.session.delete(obj)
            self.session.commit()
    
    def soft_delete(self, obj_id: int) -> None:
        """软删除对象"""
        obj = self.get_by_id(obj_id)
        if obj and hasattr(obj, 'is_deleted'):
            obj.is_deleted = True
            self.session.commit()
    
    def get_list(self, filters: Dict = None, page: int = 1, size: int = 20) -> List[Any]:
        """获取对象列表"""
        query = self.session.query(self.model_class)
        
        # 应用过滤条件
        if filters:
            query = self._apply_filters(query, filters)
        
        # 应用分页
        offset = (page - 1) * size
        return query.offset(offset).limit(size).all()
    
    def count(self, filters: Dict = None) -> int:
        """获取对象数量"""
        query = self.session.query(self.model_class)
        
        # 应用过滤条件
        if filters:
            query = self._apply_filters(query, filters)
        
        return query.count()
    
    def _apply_filters(self, query, filters: Dict):
        """应用过滤条件"""
        for key, value in filters.items():
            if hasattr(self.model_class, key):
                if isinstance(value, list):
                    query = query.filter(getattr(self.model_class, key).in_(value))
                else:
                    query = query.filter(getattr(self.model_class, key) == value)
        return query
```

#### TaskDAO - 任务数据访问对象
```python
class TaskDAO(BaseDAO):
    """任务数据访问对象"""
    
    def __init__(self, session: Session):
        super().__init__(session, Task)
    
    def get_active_tasks(self) -> List[Task]:
        """获取活跃任务"""
        return self.session.query(Task).filter(
            Task.status == 'ACTIVE',
            Task.is_deleted == False
        ).all()
    
    def get_tasks_by_script(self, script_id: int) -> List[Task]:
        """根据脚本ID获取任务"""
        return self.session.query(Task).filter(
            Task.script_id == script_id,
            Task.is_deleted == False
        ).all()
    
    def get_scheduled_tasks(self) -> List[Task]:
        """获取需要调度的任务"""
        return self.session.query(Task).filter(
            Task.status == 'ACTIVE',
            Task.schedule_type.in_(['CRON', 'INTERVAL']),
            Task.is_deleted == False
        ).all()
    
    def search_tasks(self, keyword: str, page: int = 1, size: int = 20) -> List[Task]:
        """搜索任务"""
        query = self.session.query(Task).filter(
            Task.name.contains(keyword),
            Task.is_deleted == False
        )
        
        offset = (page - 1) * size
        return query.offset(offset).limit(size).all()
```

## 🗄️ 数据库设计

### 4.1 数据库架构

#### SQLite数据库选择
```python
# 数据库配置
DATABASE_CONFIG = {
    'engine': 'sqlite',
    'database': 'task_manager.db',
    'echo': False,  # 生产环境关闭SQL日志
    'pool_pre_ping': True,
    'pool_recycle': 3600,
    'connect_args': {
        'check_same_thread': False,
        'timeout': 30
    }
}

# SQLAlchemy引擎配置
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

engine = create_engine(
    f"sqlite:///{DATABASE_CONFIG['database']}",
    echo=DATABASE_CONFIG['echo'],
    pool_pre_ping=DATABASE_CONFIG['pool_pre_ping'],
    connect_args=DATABASE_CONFIG['connect_args']
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
```

#### 数据库连接管理
```python
class DatabaseManager:
    """数据库连接管理器"""

    def __init__(self):
        self.engine = None
        self.session_factory = None

    def initialize(self, database_url: str):
        """初始化数据库连接"""
        self.engine = create_engine(database_url)
        self.session_factory = sessionmaker(bind=self.engine)

        # 创建表结构
        Base.metadata.create_all(self.engine)

    def get_session(self):
        """获取数据库会话"""
        return self.session_factory()

    def close(self):
        """关闭数据库连接"""
        if self.engine:
            self.engine.dispose()

# 数据库会话上下文管理器
from contextlib import contextmanager

@contextmanager
def get_db_session():
    """数据库会话上下文管理器"""
    session = DatabaseManager.instance().get_session()
    try:
        yield session
        session.commit()
    except Exception:
        session.rollback()
        raise
    finally:
        session.close()
```

### 4.2 数据模型定义

#### 基础模型类
```python
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text
from datetime import datetime

Base = declarative_base()

class BaseModel(Base):
    """基础模型类"""
    __abstract__ = True

    id = Column(Integer, primary_key=True, autoincrement=True)
    created_at = Column(DateTime, default=datetime.now, nullable=False)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, nullable=False)
    is_deleted = Column(Boolean, default=False, nullable=False)

    def to_dict(self):
        """转换为字典"""
        return {
            column.name: getattr(self, column.name)
            for column in self.__table__.columns
        }

    def __repr__(self):
        return f"<{self.__class__.__name__}(id={self.id})>"
```

#### 任务模型
```python
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey
from sqlalchemy.orm import relationship

class Task(BaseModel):
    """任务模型"""
    __tablename__ = 'tasks'

    name = Column(String(255), nullable=False, comment='任务名称')
    description = Column(Text, comment='任务描述')
    script_id = Column(Integer, ForeignKey('scripts.id'), nullable=False, comment='关联脚本ID')

    # 调度配置
    schedule_type = Column(String(50), nullable=False, comment='调度类型: ONCE/INTERVAL/CRON')
    schedule_config = Column(Text, comment='调度配置JSON')

    # 执行配置
    parameters = Column(Text, comment='执行参数JSON')
    environment = Column(Text, comment='环境变量JSON')
    working_directory = Column(String(500), comment='工作目录')
    timeout = Column(Integer, default=3600, comment='超时时间(秒)')

    # 状态信息
    status = Column(String(50), default='INACTIVE', comment='任务状态')
    priority = Column(Integer, default=5, comment='优先级(1-10)')

    # 统计信息
    total_runs = Column(Integer, default=0, comment='总执行次数')
    success_runs = Column(Integer, default=0, comment='成功执行次数')
    last_run_time = Column(DateTime, comment='最后执行时间')
    next_run_time = Column(DateTime, comment='下次执行时间')

    # 关联关系
    script = relationship("Script", back_populates="tasks")
    executions = relationship("ExecutionLog", back_populates="task")

    def __repr__(self):
        return f"<Task(id={self.id}, name='{self.name}', status='{self.status}')>"
```

#### 脚本模型
```python
class Script(BaseModel):
    """脚本模型"""
    __tablename__ = 'scripts'

    name = Column(String(255), nullable=False, comment='脚本名称')
    description = Column(Text, comment='脚本描述')
    script_type = Column(String(50), nullable=False, comment='脚本类型: PYTHON/SHELL/BATCH')

    # 脚本内容
    content = Column(Text, nullable=False, comment='脚本内容')
    file_path = Column(String(500), comment='脚本文件路径')
    file_size = Column(Integer, comment='文件大小(字节)')
    file_hash = Column(String(64), comment='文件哈希值')

    # 版本信息
    version = Column(String(50), default='1.0.0', comment='版本号')

    # 参数定义
    parameters_schema = Column(Text, comment='参数定义JSON Schema')

    # 统计信息
    usage_count = Column(Integer, default=0, comment='使用次数')
    last_used_time = Column(DateTime, comment='最后使用时间')

    # 关联关系
    tasks = relationship("Task", back_populates="script")
    versions = relationship("ScriptVersion", back_populates="script")

    def __repr__(self):
        return f"<Script(id={self.id}, name='{self.name}', type='{self.script_type}')>"
```

#### 执行日志模型
```python
class ExecutionLog(BaseModel):
    """执行日志模型"""
    __tablename__ = 'execution_logs'

    task_id = Column(Integer, ForeignKey('tasks.id'), nullable=False, comment='任务ID')

    # 执行信息
    status = Column(String(50), nullable=False, comment='执行状态')
    start_time = Column(DateTime, nullable=False, comment='开始时间')
    end_time = Column(DateTime, comment='结束时间')
    duration = Column(Integer, comment='执行时长(秒)')

    # 执行结果
    exit_code = Column(Integer, comment='退出码')
    output = Column(Text, comment='标准输出')
    error_output = Column(Text, comment='错误输出')
    result_data = Column(Text, comment='结果数据JSON')
    error_message = Column(Text, comment='错误信息')

    # 资源使用
    cpu_usage = Column(Integer, comment='CPU使用率(%)')
    memory_usage = Column(Integer, comment='内存使用量(MB)')

    # 执行环境
    execution_host = Column(String(255), comment='执行主机')
    process_id = Column(Integer, comment='进程ID')

    # 关联关系
    task = relationship("Task", back_populates="executions")

    def __repr__(self):
        return f"<ExecutionLog(id={self.id}, task_id={self.task_id}, status='{self.status}')>"
```

## 🔌 接口设计

### 5.1 服务接口定义

#### ITaskService - 任务服务接口
```python
from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any

class ITaskService(ABC):
    """任务服务接口"""

    @abstractmethod
    def create_task(self, task_data: Dict[str, Any]) -> Task:
        """创建任务"""
        pass

    @abstractmethod
    def update_task(self, task_id: int, task_data: Dict[str, Any]) -> Task:
        """更新任务"""
        pass

    @abstractmethod
    def delete_task(self, task_id: int) -> None:
        """删除任务"""
        pass

    @abstractmethod
    def get_task(self, task_id: int) -> Optional[Task]:
        """获取任务"""
        pass

    @abstractmethod
    def get_tasks(self, filters: Dict = None, page: int = 1, size: int = 20) -> List[Task]:
        """获取任务列表"""
        pass

    @abstractmethod
    def search_tasks(self, keyword: str, page: int = 1, size: int = 20) -> List[Task]:
        """搜索任务"""
        pass

    @abstractmethod
    def execute_task(self, task_id: int) -> int:
        """执行任务"""
        pass

    @abstractmethod
    def stop_task(self, execution_id: int) -> None:
        """停止任务"""
        pass
```

#### IScriptService - 脚本服务接口
```python
class IScriptService(ABC):
    """脚本服务接口"""

    @abstractmethod
    def create_script(self, script_data: Dict[str, Any]) -> Script:
        """创建脚本"""
        pass

    @abstractmethod
    def update_script(self, script_id: int, script_data: Dict[str, Any]) -> Script:
        """更新脚本"""
        pass

    @abstractmethod
    def delete_script(self, script_id: int) -> None:
        """删除脚本"""
        pass

    @abstractmethod
    def get_script(self, script_id: int) -> Optional[Script]:
        """获取脚本"""
        pass

    @abstractmethod
    def get_scripts(self, filters: Dict = None, page: int = 1, size: int = 20) -> List[Script]:
        """获取脚本列表"""
        pass

    @abstractmethod
    def test_script(self, script_id: int, parameters: Dict = None) -> Dict[str, Any]:
        """测试脚本"""
        pass

    @abstractmethod
    def validate_script(self, script_content: str, script_type: str) -> Dict[str, Any]:
        """验证脚本"""
        pass
```

### 5.2 数据传输对象

#### TaskDTO - 任务数据传输对象
```python
from dataclasses import dataclass
from typing import Optional, Dict, Any
from datetime import datetime

@dataclass
class TaskDTO:
    """任务数据传输对象"""
    id: Optional[int] = None
    name: str = ""
    description: str = ""
    script_id: int = 0
    schedule_type: str = "ONCE"
    schedule_config: Dict[str, Any] = None
    parameters: Dict[str, Any] = None
    environment: Dict[str, Any] = None
    working_directory: str = ""
    timeout: int = 3600
    status: str = "INACTIVE"
    priority: int = 5
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            key: value for key, value in self.__dict__.items()
            if value is not None
        }

    @classmethod
    def from_model(cls, task: Task) -> 'TaskDTO':
        """从模型创建DTO"""
        return cls(
            id=task.id,
            name=task.name,
            description=task.description,
            script_id=task.script_id,
            schedule_type=task.schedule_type,
            schedule_config=json.loads(task.schedule_config or '{}'),
            parameters=json.loads(task.parameters or '{}'),
            environment=json.loads(task.environment or '{}'),
            working_directory=task.working_directory,
            timeout=task.timeout,
            status=task.status,
            priority=task.priority,
            created_at=task.created_at,
            updated_at=task.updated_at
        )
```

#### ScriptDTO - 脚本数据传输对象
```python
@dataclass
class ScriptDTO:
    """脚本数据传输对象"""
    id: Optional[int] = None
    name: str = ""
    description: str = ""
    script_type: str = "PYTHON"
    content: str = ""
    file_path: str = ""
    version: str = "1.0.0"
    parameters_schema: Dict[str, Any] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            key: value for key, value in self.__dict__.items()
            if value is not None
        }

    @classmethod
    def from_model(cls, script: Script) -> 'ScriptDTO':
        """从模型创建DTO"""
        return cls(
            id=script.id,
            name=script.name,
            description=script.description,
            script_type=script.script_type,
            content=script.content,
            file_path=script.file_path,
            version=script.version,
            parameters_schema=json.loads(script.parameters_schema or '{}'),
            created_at=script.created_at,
            updated_at=script.updated_at
        )
```

## 🔒 安全架构

### 6.1 脚本执行安全

#### 沙箱执行环境
```python
import subprocess
import tempfile
import os
from pathlib import Path

class ScriptSandbox:
    """脚本沙箱执行环境"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.temp_dir = None
        self.allowed_paths = config.get('allowed_paths', [])
        self.blocked_imports = config.get('blocked_imports', [])
        self.resource_limits = config.get('resource_limits', {})

    def execute_python_script(self, script_content: str, parameters: Dict = None) -> Dict[str, Any]:
        """在沙箱中执行Python脚本"""
        try:
            # 创建临时目录
            self.temp_dir = tempfile.mkdtemp(prefix='script_sandbox_')

            # 创建受限的执行环境
            restricted_globals = self._create_restricted_globals()

            # 准备脚本参数
            script_locals = {'params': parameters or {}}

            # 执行脚本
            exec(script_content, restricted_globals, script_locals)

            # 获取执行结果
            result = script_locals.get('result', None)

            return {
                'success': True,
                'result': result,
                'output': '',
                'error': None
            }

        except Exception as e:
            return {
                'success': False,
                'result': None,
                'output': '',
                'error': str(e)
            }
        finally:
            # 清理临时目录
            if self.temp_dir and os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)

    def _create_restricted_globals(self) -> Dict[str, Any]:
        """创建受限的全局环境"""
        # 基础安全的内置函数
        safe_builtins = {
            'len', 'str', 'int', 'float', 'bool', 'list', 'dict', 'tuple',
            'set', 'range', 'enumerate', 'zip', 'map', 'filter', 'sorted',
            'min', 'max', 'sum', 'abs', 'round', 'print'
        }

        restricted_builtins = {
            name: __builtins__[name]
            for name in safe_builtins
            if name in __builtins__
        }

        # 允许的模块
        allowed_modules = {
            'json': __import__('json'),
            'datetime': __import__('datetime'),
            'math': __import__('math'),
            're': __import__('re'),
        }

        return {
            '__builtins__': restricted_builtins,
            **allowed_modules
        }
```

#### 权限控制系统
```python
class PermissionManager:
    """权限管理器"""

    def __init__(self):
        self.permissions = {
            'file_system': {
                'read_paths': [],
                'write_paths': [],
                'blocked_paths': [
                    'C:\\Windows\\System32',
                    'C:\\Program Files',
                    'C:\\Users\\<USER>\\AppData'
                ]
            },
            'network': {
                'allowed_hosts': [],
                'blocked_hosts': [],
                'allowed_ports': [80, 443, 8080]
            },
            'system': {
                'allow_subprocess': False,
                'allow_file_operations': True,
                'allow_network_access': False
            }
        }

    def check_file_access(self, file_path: str, operation: str) -> bool:
        """检查文件访问权限"""
        path = Path(file_path).resolve()

        # 检查是否在阻止列表中
        for blocked_pattern in self.permissions['file_system']['blocked_paths']:
            if path.match(blocked_pattern):
                return False

        # 检查是否在允许列表中
        if operation == 'read':
            allowed_paths = self.permissions['file_system']['read_paths']
        elif operation == 'write':
            allowed_paths = self.permissions['file_system']['write_paths']
        else:
            return False

        for allowed_pattern in allowed_paths:
            if path.match(allowed_pattern):
                return True

        return False

    def check_network_access(self, host: str, port: int) -> bool:
        """检查网络访问权限"""
        if not self.permissions['system']['allow_network_access']:
            return False

        # 检查主机权限
        if host in self.permissions['network']['blocked_hosts']:
            return False

        if self.permissions['network']['allowed_hosts']:
            if host not in self.permissions['network']['allowed_hosts']:
                return False

        # 检查端口权限
        if port not in self.permissions['network']['allowed_ports']:
            return False

        return True
```

### 6.2 数据安全

#### 数据加密
```python
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import os

class DataEncryption:
    """数据加密工具"""

    def __init__(self, password: str = None):
        self.password = password or self._generate_password()
        self.key = self._derive_key(self.password)
        self.cipher = Fernet(self.key)

    def encrypt(self, data: str) -> str:
        """加密数据"""
        encrypted_data = self.cipher.encrypt(data.encode())
        return base64.urlsafe_b64encode(encrypted_data).decode()

    def decrypt(self, encrypted_data: str) -> str:
        """解密数据"""
        encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
        decrypted_data = self.cipher.decrypt(encrypted_bytes)
        return decrypted_data.decode()

    def _generate_password(self) -> str:
        """生成随机密码"""
        return base64.urlsafe_b64encode(os.urandom(32)).decode()

    def _derive_key(self, password: str) -> bytes:
        """从密码派生密钥"""
        salt = b'task_manager_salt'  # 在实际应用中应该使用随机盐
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return key

# 敏感配置加密存储
class SecureConfig:
    """安全配置管理"""

    def __init__(self, config_file: str):
        self.config_file = config_file
        self.encryption = DataEncryption()
        self._config = {}

    def set_secure_value(self, key: str, value: str):
        """设置加密值"""
        encrypted_value = self.encryption.encrypt(value)
        self._config[key] = {
            'encrypted': True,
            'value': encrypted_value
        }

    def get_secure_value(self, key: str) -> str:
        """获取解密值"""
        if key in self._config and self._config[key].get('encrypted'):
            return self.encryption.decrypt(self._config[key]['value'])
        return self._config.get(key, {}).get('value', '')

    def save(self):
        """保存配置"""
        with open(self.config_file, 'w') as f:
            json.dump(self._config, f, indent=2)

    def load(self):
        """加载配置"""
        if os.path.exists(self.config_file):
            with open(self.config_file, 'r') as f:
                self._config = json.load(f)
```

---

**文档版本**: 1.0
**最后更新**: 2024-12-19
**文档状态**: 已完成
