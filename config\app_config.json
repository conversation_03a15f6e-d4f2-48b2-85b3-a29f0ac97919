{"app": {"name": "TaskManager10", "language": "zh_CN"}, "startup": {"auto_start": false, "minimize_to_tray": false, "restore_window_state": true}, "theme": {"current": "light", "auto_switch": true, "enable_custom": false}, "task": {"default_timeout": 300, "max_retry_count": 3, "max_concurrent": 5, "refresh_interval": 5, "auto_cleanup": true}, "script": {"default_editor": "", "syntax_highlighting": true, "auto_save": true, "auto_save_interval": 30, "python_path": "python", "work_directory": ""}, "log": {"level": "DEBUG", "console_output": true, "file_path": "logs/app.log", "max_file_size": 10, "backup_count": 5, "retention_days": 30}, "performance": {"max_memory_usage": 73, "max_cpu_usage": 61, "enable_cache": true, "cache_size": 100, "cache_ttl": 30}}