"""
脚本版本数据访问对象

提供脚本版本相关的数据访问操作。
"""

from typing import List, Optional, Dict, Any
from sqlalchemy import and_, desc

from ..models.script_version import ScriptVersion
from ..utils.logger import get_logger
from .base_dao import BaseDAO
from .exceptions import ValidationError, EntityNotFoundError

logger = get_logger(__name__)


class ScriptVersionDAO(BaseDAO[ScriptVersion]):
    """脚本版本数据访问对象"""
    
    def __init__(self):
        """初始化ScriptVersionDAO"""
        super().__init__(ScriptVersion)
    
    def get_by_script_id(self, script_id: int, 
                        include_deleted: bool = False) -> List[ScriptVersion]:
        """
        根据脚本ID获取版本列表
        
        Args:
            script_id: 脚本ID
            include_deleted: 是否包含已删除的版本
            
        Returns:
            版本列表
        """
        try:
            with self.get_session() as session:
                query = session.query(ScriptVersion).filter(
                    ScriptVersion.script_id == script_id
                )
                
                if not include_deleted:
                    query = query.filter(ScriptVersion.is_deleted == False)
                
                return query.order_by(desc(ScriptVersion.created_at)).all()
        except Exception as e:
            logger.error(f"Error getting script versions by script_id {script_id}: {e}")
            raise
    
    def get_current_version(self, script_id: int) -> Optional[ScriptVersion]:
        """
        获取脚本的当前版本
        
        Args:
            script_id: 脚本ID
            
        Returns:
            当前版本实例或None
        """
        try:
            with self.get_session() as session:
                return session.query(ScriptVersion).filter(
                    and_(
                        ScriptVersion.script_id == script_id,
                        ScriptVersion.is_current == True,
                        ScriptVersion.is_deleted == False
                    )
                ).first()
        except Exception as e:
            logger.error(f"Error getting current version for script {script_id}: {e}")
            raise
    
    def get_by_version(self, script_id: int, version: str) -> Optional[ScriptVersion]:
        """
        根据版本号获取脚本版本
        
        Args:
            script_id: 脚本ID
            version: 版本号
            
        Returns:
            版本实例或None
        """
        try:
            with self.get_session() as session:
                return session.query(ScriptVersion).filter(
                    and_(
                        ScriptVersion.script_id == script_id,
                        ScriptVersion.version == version,
                        ScriptVersion.is_deleted == False
                    )
                ).first()
        except Exception as e:
            logger.error(f"Error getting script version {version} for script {script_id}: {e}")
            raise
    
    def get_stable_versions(self, script_id: int) -> List[ScriptVersion]:
        """
        获取脚本的稳定版本列表
        
        Args:
            script_id: 脚本ID
            
        Returns:
            稳定版本列表
        """
        try:
            with self.get_session() as session:
                return session.query(ScriptVersion).filter(
                    and_(
                        ScriptVersion.script_id == script_id,
                        ScriptVersion.is_stable == True,
                        ScriptVersion.is_deleted == False
                    )
                ).order_by(desc(ScriptVersion.created_at)).all()
        except Exception as e:
            logger.error(f"Error getting stable versions for script {script_id}: {e}")
            raise
    
    def set_current_version(self, script_id: int, version_id: int) -> ScriptVersion:
        """
        设置当前版本
        
        Args:
            script_id: 脚本ID
            version_id: 版本ID
            
        Returns:
            设置为当前的版本实例
        """
        try:
            with self.get_session() as session:
                # 取消所有当前版本标记
                session.query(ScriptVersion).filter(
                    and_(
                        ScriptVersion.script_id == script_id,
                        ScriptVersion.is_current == True
                    )
                ).update({"is_current": False})
                
                # 设置新的当前版本
                version = session.query(ScriptVersion).filter(
                    ScriptVersion.id == version_id
                ).first()
                
                if version and version.script_id == script_id:
                    version.set_as_current()
                    session.flush()
                    session.refresh(version)
                    logger.debug(f"Set version {version_id} as current for script {script_id}")
                    return version
                else:
                    raise EntityNotFoundError("ScriptVersion", version_id)
        except EntityNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Error setting current version: {e}")
            raise
    
    def create_version(self, script_id: int, version: str, content: str,
                      description: Optional[str] = None,
                      is_stable: bool = True,
                      set_as_current: bool = False) -> ScriptVersion:
        """
        创建新版本
        
        Args:
            script_id: 脚本ID
            version: 版本号
            content: 脚本内容
            description: 版本描述
            is_stable: 是否为稳定版本
            set_as_current: 是否设置为当前版本
            
        Returns:
            创建的版本实例
        """
        try:
            # 计算内容校验和
            import hashlib
            checksum = hashlib.sha256(content.encode('utf-8')).hexdigest()
            
            # 创建版本数据
            version_data = {
                "script_id": script_id,
                "version": version,
                "content": content,
                "description": description,
                "is_stable": is_stable,
                "is_current": False,  # 先创建为非当前版本
                "checksum": checksum
            }
            
            # 创建版本
            script_version = self.create(**version_data)
            
            # 如果需要设置为当前版本
            if set_as_current:
                script_version = self.set_current_version(script_id, script_version.id)
            
            logger.debug(f"Created version {version} for script {script_id}")
            return script_version
            
        except Exception as e:
            logger.error(f"Error creating script version: {e}")
            raise
    
    def _validate_create_data(self, data: Dict[str, Any]) -> None:
        """
        验证脚本版本创建数据
        
        Args:
            data: 创建数据
            
        Raises:
            ValidationError: 验证失败
        """
        # 验证必需字段
        if not data.get('script_id'):
            raise ValidationError('script_id', data.get('script_id'), 'Script ID is required')
        
        if not data.get('version'):
            raise ValidationError('version', data.get('version'), 'Version is required')
        
        if not data.get('content'):
            raise ValidationError('content', data.get('content'), 'Content is required')
        
        # 验证脚本存在性
        from .script_dao import ScriptDAO
        script_dao = ScriptDAO()
        if not script_dao.exists(data['script_id']):
            raise ValidationError('script_id', data['script_id'], 'Script does not exist')
        
        # 验证版本唯一性
        if self.get_by_version(data['script_id'], data['version']):
            raise ValidationError('version', data['version'], 'Version already exists for this script')
